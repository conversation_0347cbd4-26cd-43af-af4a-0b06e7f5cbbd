import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { BucketFileDto } from '../../buckets/dtos/models';
import type { ListStyle } from '../../enums/list-style.enum';
import type { CollectionType } from '../../enums/collection-type.enum';

export interface CollectionArticleSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
  status?: PublishStatus;
}

export interface CollectionDto extends EntityDto<number> {
  parentCollectionId?: number;
  contentCodeName?: string;
  contentCodeId: number;
  languageCode?: string;
  thumbnailFileId?: number;
  thumbnailBucketFile: BucketFileDto;
  collectionName?: string;
  title?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  listStyle?: ListStyle;
  collectionType?: CollectionType;
  renderAsOneSet: boolean;
  publishDate?: string;
  status?: PublishStatus;
  memo?: string;
}

export interface CollectionFileSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
}

export interface CollectionSearchDto extends PagedAndSortedResultRequestDto {
  status?: PublishStatus;
  channelId?: number;
}

export interface CollectionToArticleDto {
  articleId: number;
  weight: number;
}

export interface CollectionToFileDto {
  fileId: number;
  weight: number;
}
