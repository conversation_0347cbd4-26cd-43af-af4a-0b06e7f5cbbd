import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-pavelistcontent',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="pavelistcontent-container p-6">
      <h1 class="text-2xl font-bold mb-6">平铺列表内容</h1>
      
      <!-- 筛选和排序控件 -->
      <div class="flex flex-wrap items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center space-x-4">
          <select class="px-3 py-2 border rounded-md">
            <option>全部分类</option>
            <option>分类 1</option>
            <option>分类 2</option>
          </select>
          <select class="px-3 py-2 border rounded-md">
            <option>按时间排序</option>
            <option>按热度排序</option>
            <option>按标题排序</option>
          </select>
        </div>
        <div class="flex items-center space-x-2">
          <button class="p-2 border rounded hover:bg-gray-100" title="网格视图">⚏</button>
          <button class="p-2 border rounded hover:bg-gray-100 bg-blue-100" title="列表视图">☰</button>
        </div>
      </div>
      
      <!-- 平铺内容区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- 内容项 1 -->
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
          <div class="aspect-video bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white">
            <span class="text-2xl">📄</span>
          </div>
          <div class="p-4">
            <h3 class="font-semibold text-sm mb-2 line-clamp-2">内容标题示例 1</h3>
            <p class="text-xs text-gray-600 mb-3 line-clamp-3">这是内容的简短描述，展示主要信息...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2024-01-01</span>
              <span>123 views</span>
            </div>
          </div>
        </div>
        
        <!-- 内容项 2 -->
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
          <div class="aspect-video bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center text-white">
            <span class="text-2xl">🎵</span>
          </div>
          <div class="p-4">
            <h3 class="font-semibold text-sm mb-2 line-clamp-2">内容标题示例 2</h3>
            <p class="text-xs text-gray-600 mb-3 line-clamp-3">另一个内容的描述信息...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2024-01-02</span>
              <span>456 views</span>
            </div>
          </div>
        </div>
        
        <!-- 内容项 3 -->
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
          <div class="aspect-video bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center text-white">
            <span class="text-2xl">🎬</span>
          </div>
          <div class="p-4">
            <h3 class="font-semibold text-sm mb-2 line-clamp-2">内容标题示例 3</h3>
            <p class="text-xs text-gray-600 mb-3 line-clamp-3">第三个内容项的描述...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2024-01-03</span>
              <span>789 views</span>
            </div>
          </div>
        </div>
        
        <!-- 内容项 4 -->
        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden">
          <div class="aspect-video bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center text-white">
            <span class="text-2xl">📚</span>
          </div>
          <div class="p-4">
            <h3 class="font-semibold text-sm mb-2 line-clamp-2">内容标题示例 4</h3>
            <p class="text-xs text-gray-600 mb-3 line-clamp-3">第四个内容项的描述信息...</p>
            <div class="flex items-center justify-between text-xs text-gray-500">
              <span>2024-01-04</span>
              <span>101 views</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="flex items-center justify-center mt-8">
        <div class="flex items-center space-x-2">
          <button class="px-3 py-2 border rounded hover:bg-gray-100">上一页</button>
          <button class="px-3 py-2 bg-blue-500 text-white rounded">1</button>
          <button class="px-3 py-2 border rounded hover:bg-gray-100">2</button>
          <button class="px-3 py-2 border rounded hover:bg-gray-100">3</button>
          <button class="px-3 py-2 border rounded hover:bg-gray-100">下一页</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pavelistcontent-container {
      min-height: 400px;
    }
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .aspect-video {
      aspect-ratio: 16 / 9;
    }
  `]
})
export class PavelistcontentComponent {
  constructor() { }
}
