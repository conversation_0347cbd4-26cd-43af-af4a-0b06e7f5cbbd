import type { DefaultOrderByField } from '../enums/default-order-by-field.enum';

export interface ArticleSummaryResult {
  id: number;
  thumbnailUrl?: string;
  description?: string;
  title?: string;
  creationTime?: string;
  lastModificationTime?: string;
  deliveryDate?: string;
}

export interface CollectionSummaryRequest {
  skip: number;
  maxResultCount: number;
  sorting?: string;
  year?: number;
  month?: number;
}

export interface CollectionSummaryResult {
  id: number;
  name?: string;
  description?: string;
  contentCode?: string;
  orderByField?: DefaultOrderByField;
  totalRecords: number;
  articles: ArticleSummaryResult[];
}
