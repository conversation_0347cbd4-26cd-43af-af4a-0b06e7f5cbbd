import type { EntityDto } from '@abp/ng.core';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentCategory } from '../../enums/content-category.enum';

export interface BucketFileDto extends EntityDto<number> {
  fileName?: string;
  title?: string;
  relativePathInBucket?: string;
  languageCode?: string;
  mediaType?: MediaType;
  contentCategory?: ContentCategory;
  deliveryDate?: string;
  views: number;
  youtubeId?: string;
}

export interface BucketToFileDto {
  bucketId: number;
  bucketName?: string;
  bucketDomain?: string;
  fileId: number;
  fileName?: string;
}
