using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Tags;
using HolyBless.Results;
using HolyBless.Tags.Dtos;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Articles
{
    public class ReadOnlyArticleAppService : ApplicationService, IReadOnlyArticleAppService
    {
        protected readonly IRepository<Article, int> _repository;
        protected readonly IRepository<ArticleToTag> _articleToTagRepository;
        protected readonly IRepository<Tag, int> _tagRepository;
        protected readonly IRepository<TeacherArticleLink> _teacherArticleLinkRepository;
        protected readonly IRepository<ArticleFile, int> _articleFileRepository;
        protected readonly IRepository<BucketFile, int> _bucketFileRepository;

        public ReadOnlyArticleAppService(
            IRepository<Article, int> repository,
            IRepository<ArticleToTag> articleToTagRepository,
            IRepository<Tag, int> tagRepository,
            IRepository<TeacherArticleLink> teacherArticleLinkRepository,
            IRepository<ArticleFile, int> articleFileRepository,
            IRepository<BucketFile, int> bucketFileRepository)
        {
            _repository = repository;
            _articleToTagRepository = articleToTagRepository;
            _tagRepository = tagRepository;
            _teacherArticleLinkRepository = teacherArticleLinkRepository;
            _articleFileRepository = articleFileRepository;
            _bucketFileRepository = bucketFileRepository;
        }

        public async Task<ArticleDto> GetAsync(int id)
        {
            var queryable = await _repository.GetQueryableAsync();
            var article = await queryable
                .Include(x => x.ThumbnailBucketFile)
                .Include(x => x.ArticleToTags)
                .Include(x => x.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), id);
            }

            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        public async Task<PagedResultDto<ArticleDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.ThumbnailBucketFile)
                .Include(x => x.ArticleToTags)
                // ArticleFiles are not included when retrieving a list to optimize performance
                .OrderBy(input.Sorting ?? "Title")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var articles = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ArticleDto>(
                totalCount,
                ObjectMapper.Map<List<Article>, List<ArticleDto>>(articles)
            );
        }

        public async Task<List<TagDto>> GetTagsAsync(int articleId)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Get article tags using a join
            var articleTagsQueryable = await _articleToTagRepository.GetQueryableAsync();
            var tagsQueryable = await _tagRepository.GetQueryableAsync();

            var query = from articleTag in articleTagsQueryable
                        join tag in tagsQueryable on articleTag.TagId equals tag.Id
                        where articleTag.ArticleId == articleId
                        select tag;

            // Include ContentCode to properly map to TagDto
            query = query.Include(t => t.ContentCode);

            var tags = await AsyncExecuter.ToListAsync(query);

            return ObjectMapper.Map<List<Tag>, List<TagDto>>(tags);
        }

        public async Task<PagedResultDto<TeacherArticleLinkDto>> GetTeacherArticleLinksAsync(int studentArticleId, int skipCount, int maxResultCount, string? sorting = null)
        {
            var queryable = await _teacherArticleLinkRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.StudentArticleId == studentArticleId)
                .OrderBy(sorting ?? nameof(TeacherArticleLink.Weight));
            var items = await AsyncExecuter.ToListAsync(
                query.Skip(skipCount).Take(maxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);
            var dtos = ObjectMapper.Map<List<TeacherArticleLink>, List<TeacherArticleLinkDto>>(items);
            return new PagedResultDto<TeacherArticleLinkDto>(totalCount, dtos);
        }

        public async Task<ArticleAggregateResult> GetArticleAggregatesAsync(int articleId)
        {
            // Get article queryable
            var articleQueryable = await _repository.GetQueryableAsync();
            var articleFileQueryable = await _articleFileRepository.GetQueryableAsync();
            var bucketFileQueryable = await _bucketFileRepository.GetQueryableAsync();

            // Main query to get article with thumbnail info
            var articleQuery = from a in articleQueryable
                               join th in bucketFileQueryable on a.ThumbnailFileId equals th.Id into thumbnails
                               from thumbnail in thumbnails.DefaultIfEmpty()
                               where a.Id == articleId
                               select new ArticleAggregateResult
                               {
                                   // Article properties
                                   Id = a.Id,
                                   Title = a.Title,
                                   Description = a.Description,
                                   Keywords = a.Keywords,
                                   Views = a.Views,
                                   Likes = a.Likes,
                                   DeliveryDate = a.DeliveryDate,
                                   ArticleContentCategory = a.ArticleContentCategory,
                                   Status = a.Status,
                                   Content = a.Content,
                                   Memo = a.Memo,
                                   CreationTime = a.CreationTime,
                                   LastModificationTime = a.LastModificationTime,

                                   // Thumbnail file info
                                   ThumbnailFileId = a.ThumbnailFileId,
                                   ThumbnailFileName = thumbnail == null ? null : thumbnail.FileName,
                                   ThumbnailRelativePathInBucket = thumbnail == null ? null : thumbnail.RelativePathInBucket,
                                   ThumbnailMediaType = thumbnail == null ? null : thumbnail.MediaType,
                                   ThumbnailUrl = thumbnail == null ? null : Path.Combine(thumbnail.RelativePathInBucket, thumbnail.FileName)
                               };

            var result = await AsyncExecuter.FirstOrDefaultAsync(articleQuery);

            if (result == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Get article files with bucket file info
            var articleFilesQuery = from af in articleFileQueryable
                                    join bf in bucketFileQueryable on af.FileId equals bf.Id
                                    where af.ArticleId == articleId
                                    orderby af.IsPrimary descending, af.CreationTime
                                    select new ArticleFileAggregateResult
                                    {
                                        // ArticleFile properties
                                        Id = af.Id,
                                        ArticleId = af.ArticleId,
                                        FileId = af.FileId,
                                        Title = af.Title,
                                        Description = af.Description,
                                        IsPrimary = af.IsPrimary,

                                        // BucketFile properties
                                        FileName = bf.FileName,
                                        FileTitle = bf.Title,
                                        RelativePathInBucket = bf.RelativePathInBucket,
                                        MediaType = bf.MediaType,
                                        ContentCategory = bf.ContentCategory,
                                        FileDeliveryDate = bf.DeliveryDate,
                                        FileViews = bf.Views,
                                        YoutubeId = bf.YoutubeId
                                    };

            result.ArticleFiles = await AsyncExecuter.ToListAsync(articleFilesQuery);

            return result;
        }
    }
}