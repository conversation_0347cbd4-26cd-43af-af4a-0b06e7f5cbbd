import { Component, ElementRef, inject, ViewChild, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { StyleClassModule } from 'primeng/styleclass';
import { LayoutService } from '@/layout/service/layout.service';
import { InputText } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { FormsModule } from '@angular/forms';
import { AppSidebar } from './app.sidebar';
import { TranslateService } from '@ngx-translate/core';
import { transition } from '@angular/animations';

//This component is the top right icon menu items (search, profile, settings, arrow area)
@Component({
    selector: '[app-topbar]',
    standalone: true,
    imports: [RouterModule, CommonModule, StyleClassModule, FormsModule, AppSidebar, InputText, ButtonModule, IconField, InputIcon],
    templateUrl: './app.topbar.html',
    host: {
        class: 'layout-topbar'
    }
})
export class AppTopbar implements OnInit {
    layoutService = inject(LayoutService);
    tranlateService = inject(TranslateService);

    el = inject(ElementRef);

    @ViewChild('menubutton') menuButton!: ElementRef;

    @ViewChild(AppSidebar) appSidebar!: AppSidebar;

    ngOnInit(): void {
        // 新增：设置默认语言
        const browserLang = this.tranlateService.getBrowserLang();
        const defaultLang = browserLang === 'zh' ? 'zh' : 'en';
        this.tranlateService.setDefaultLang(defaultLang);
        this.tranlateService.use(defaultLang);
    }

    // 新增：切换语言的方法
    toggleLanguage() {
        const currentLang = this.tranlateService.currentLang || 'en';
        const newLang = currentLang === 'en' ? 'zh' : 'en';
        this.tranlateService.use(newLang);
        localStorage.setItem('lang', newLang); // 持久化语言选择
    }
    onMenuButtonClick() {
        this.layoutService.onMenuToggle();
    }

    onConfigButtonClick() {
        this.layoutService.toggleConfigSidebar();
    }

    onRightMenuButtonClick() {
        this.layoutService.showRightMenu();
    }

    onTopbarItemClick() {
        document.body.click();
    }
}
