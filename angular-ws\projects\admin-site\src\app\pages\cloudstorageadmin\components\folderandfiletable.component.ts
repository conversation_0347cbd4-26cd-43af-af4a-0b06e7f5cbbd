import { Component, signal, computed, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Table, TableModule } from 'primeng/table';
import { CheckboxModule, CheckboxChangeEvent } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FileUploadModule } from 'primeng/fileupload';
import { FormsModule } from '@angular/forms';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TagModule } from 'primeng/tag';
import { DropdownModule } from 'primeng/dropdown';
import { DialogModule } from 'primeng/dialog';
import { PickListModule } from 'primeng/picklist';
import { MultiSelectModule } from 'primeng/multiselect';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
@Component({
  selector: 'app-folderandfiletable',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    CheckboxModule,
    ButtonModule,
    InputTextModule,
    FileUploadModule,
    FormsModule,
    IconFieldModule,
    InputIconModule,
    TagModule,
    DropdownModule,
    DialogModule,
    PickListModule,
    MultiSelectModule,
    ConfirmDialogModule,
    ToastModule,
  ],
  providers: [ConfirmationService, MessageService],
  template: `
    <div class="card">
      <!-- 工具栏（全选/Import/Remove按钮） -->
      <div class="flex gap-2 items-center mb-4">
        <p-checkbox
          [ngModel]="isAllSelected()"
          (onChange)="toggleSelectAll($event)"
          [binary]="true"
        ></p-checkbox>
        &nbsp;
        <button
          pButton
          icon="pi pi-upload"
          label="Import"
          [disabled]="!selectedFolderId"
          (click)="showImportDialog()"
        ></button>
        <button
          pButton
          icon="pi pi-trash"
          class="p-button-danger"
          label="Remove"
          (click)="confirmDelete()"
        ></button>
        <p-confirmDialog
          header="确认删除"
          icon="pi pi-exclamation-triangle"
          acceptButtonStyleClass="p-button-danger"
        ></p-confirmDialog>
        <p-toast></p-toast>
      </div>
      <!-- 导入对话框 -->
      <p-dialog
        header="Import Files"
        [(visible)]="displayImportDialog"
        [modal]="true"
        [style]="{ width: '50vw' }"
      >
        <p>Folder ID: {{ selectedFolderId }}</p>
        <p>Folder Name: {{ selectedFolderLabel }}</p>

        <!-- 新增的搜索控件区域 -->
        <div class="flex gap-2 items-center mb-4">
          <span class="p-input-icon-left">
            <input
              type="text"
              pInputText
              placeholder="FileName"
              [(ngModel)]="searchFileName"
            />
          </span>

          <span class="p-input-icon-left">
            <input
              type="text"
              pInputText
              placeholder="RelativePath"
              [(ngModel)]="searchRelativePath"
            />
          </span>

          <p-checkbox
            [binary]="true"
            [(ngModel)]="samePublishTime"
          ></p-checkbox>
          <span>Same PublishTime</span>

          <button
            pButton
            label="Reset"
            class="p-button-secondary"
            (click)="resetSearchFilters()"
          ></button>
        </div>

        <p-pickList
          [source]="availableFiles"
          [target]="selectedFiles()"
          sourceHeader="Available"
          targetHeader="Selected"
        >
          <ng-template let-file pTemplate="item">
            <div>{{ file.name }}</div>
          </ng-template>
        </p-pickList>
        <ng-template pTemplate="footer">
          <button
            pButton
            label="Cancel"
            icon="pi pi-times"
            (click)="displayImportDialog = false"
          ></button>
          <button
            pButton
            label="Import"
            icon="pi pi-check"
            (click)="importFiles()"
          ></button>
        </ng-template>
      </p-dialog>
      <!-- 表格 -->
      <p-table
        #dt
        [value]="files"
        [(selection)]="selectedFiles"
        (selectionChange)="selectedFiles.set($event)"
        dataKey="id"
        [rows]="10"
        [rowsPerPageOptions]="[10, 25, 50]"
        [paginator]="true"
        [filters]="filters"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 3rem"></th>
            <th pSortableColumn="fileName">
              FileName <p-sortIcon field="fileName"></p-sortIcon>
              <p-columnFilter
                type="text"
                field="fileName"
                display="menu"
              ></p-columnFilter>
            </th>
            <th pSortableColumn="contentCategory">
              ContentCategory <p-sortIcon field="contentCategory"></p-sortIcon>
              <p-columnFilter
                field="contentCategory"
                matchMode="in"
                display="menu"
                [showMatchModes]="false"
                [showOperator]="false"
                [showAddButton]="false"
              >
                <ng-template #filter let-value let-filter="filterCallback">
                  <p-multiselect
                    [ngModel]="value"
                    [options]="contentCategories"
                    placeholder="Any"
                    (onChange)="filter($event.value)"
                    [panelStyle]="{ minWidth: '16rem' }"
                  >
                    <ng-template let-option #item>
                      <div class="flex items-center gap-2">
                        <span>{{ option }}</span>
                      </div>
                    </ng-template>
                  </p-multiselect>
                </ng-template>
              </p-columnFilter>
            </th>
            <th pSortableColumn="title">
              FileTitle <p-sortIcon field="title"></p-sortIcon>
              <p-columnFilter
                type="text"
                field="title"
                display="menu"
              ></p-columnFilter>
            </th>
            <th style="width: 8rem"></th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-file>
          <tr>
            <td>
              <p-checkbox
                [ngModel]="isFileSelected(file)"
                (onChange)="toggleFileSelection(file, $event)"
                [binary]="true"
              >
              </p-checkbox>
            </td>
            <td>
              <span>{{ file.fileName }}</span>
            </td>
            <td>
              <span>{{ file.contentCategory }}</span>
            </td>
            <td>
              <span *ngIf="editingRow!.id !== file.id">{{ file.title }}</span>
              <input
                *ngIf="editingRow!.id === file.id"
                [(ngModel)]="file.title"
                pInputText
              />
            </td>
            <td class="text-right">
              <button
                *ngIf="editingRow!.id !== file.id"
                pButton
                icon="pi pi-pencil"
                class="p-button-rounded p-button-text mr-2"
                (click)="startEdit(file)"
              ></button>
              <button
                *ngIf="editingRow!.id === file.id"
                pButton
                icon="pi pi-check"
                class="p-button-rounded p-button-text p-button-success mr-2"
                (click)="saveEdit()"
              ></button>
              <button
                *ngIf="editingRow!.id === file.id"
                pButton
                icon="pi pi-times"
                class="p-button-rounded p-button-text p-button-danger"
                (click)="cancelEdit()"
              ></button>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  `,
  styles: [
    `
      .p-checkbox {
        margin-right: 0.5rem;
      }
    `,
  ],
})
export class FolderAndFileTableComponent {
  confirmationService = inject(ConfirmationService);
  messageService = inject(MessageService);

  private _files = signal<any[]>([]);
  @Input() set files(value: any[]) {
    this._files.set(value);
  }
  get files() {
    return this._files();
  }

  @Input() selectedFolderId: string | null = null;
  @Input() selectedFolderLabel: string | null = null;
  displayImportDialog = false;
  availableFiles = [
    { id: 1, name: 'document.pdf' },
    { id: 2, name: 'image.png' },
    { id: 3, name: 'report.docx' },
  ];

  editingRow: any = {};
  editingText: string | undefined = '';
  contentCategories = [
    'pdf',
    'doc',
    'excel',
    'image',
    'txt',
    'audio',
    'folder',
    'video',
  ];

  // 初始化过滤器
  filters: any = {
    fileName: [{ value: null, matchMode: 'contains' }],
    contentCategory: [{ value: [], matchMode: 'in' }], // 改为数组和'in'匹配模式
    title: [{ value: null, matchMode: 'contains' }],
  };

  // 选中状态信号（关键修复：初始化空数组）
  selectedFiles = signal<any[]>([]);

  // 在组件类中添加新属性
  searchFileName: string = '';
  searchRelativePath: string = '';
  samePublishTime: boolean = false;

  confirmDelete() {
    if (this.selectedFiles().length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: '未选择文件',
        detail: '请先选择要删除的文件'
      });
      return;
    }

    const fileNames = this.selectedFiles().map(f => f.fileName).join(', ');
    const hasFolders = this.selectedFiles().some(f => f.contentCategory === 'folder');

    this.confirmationService.confirm({
      message: `确定要删除选中的 ${this.selectedFiles().length} 个文件吗？${hasFolders ? '（包含文件夹）' : ''}`,
      header: '删除确认',
      icon: 'pi pi-info-circle',
      acceptLabel: '删除',
      rejectLabel: '取消',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.deleteSelectedFiles();
        this.messageService.add({
          severity: 'success',
          summary: '删除成功',
          detail: `已删除: ${fileNames}`
        });
      }
    });
  }

  deleteSelectedFiles() {
    // 从文件列表中过滤掉已选文件
    this.files = this.files.filter((file: any) =>
      !this.selectedFiles().some((selected: any) => selected.id === file.id)
    );

    // 清空选择
    this.selectedFiles.set([]);
  }


  // 添加reset方法
  resetSearchFilters() {
    this.searchFileName = '';
    this.searchRelativePath = '';
    this.samePublishTime = false;
  }

  showImportDialog() {
    this.displayImportDialog = true;
  }

  importFiles() {
    console.log(
      `Importing to folder ${this.selectedFolderId}:`,
      this.selectedFiles,
    );
    this.displayImportDialog = false;
    // 这里可以添加实际导入逻辑
  }
  // 在组件类中添加
  startEdit(row: any) {
    this.editingRow = row;
    this.editingText = this.editingRow!.title;
  }

  cancelEdit() {
    this.editingRow!.title = this.editingText;
    this.editingRow = {};
  }

  saveEdit() {
    this.editingRow = {};
    // 这里可以添加保存到API的逻辑
  }

  // 计算属性：是否全选（新增）
  isAllSelected = computed(
    () => this.selectedFiles().length === this.files.length,
  );

  // 判断单行是否选中（修复：防御性编程）
  isFileSelected(file: any): boolean {
    return this.selectedFiles().some((f) => f.id === file.id);
  }

  // 全选切换（修复：严格类型）
  toggleSelectAll(event: CheckboxChangeEvent) {
    const isChedked = event.checked ?? false;
    this.selectedFiles.set(isChedked ? [...this.files] : []);
  }

  // 单行切换（修复：空值保护）
  toggleFileSelection(file: any, event: CheckboxChangeEvent) {
    const isChecked = event.checked;

    this.selectedFiles.update((current) => {
      if (isChecked) {
        // 如果当前文件未被选中，则添加
        if (!current.some((f) => f.id === file.id)) {
          return [...current, file];
        }
      } else {
        // 如果当前文件已被选中，则移除
        return current.filter((f) => f.id !== file.id);
      }
      return current;
    });
  }

  uploadFile(event: any) {
    console.log('File upload triggered', event);
  }


}
