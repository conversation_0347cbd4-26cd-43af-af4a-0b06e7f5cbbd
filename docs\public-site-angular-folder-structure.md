Angular 19 Project Folder Structure
===================================

    spiritual-content-app/
    ├── src/
    │   ├── app/
    │   │   ├── core/
    │   │   │   ├── guards/
    │   │   │   │   ├── auth.guard.ts
    │   │   │   │   ├── offline.guard.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── interceptors/
    │   │   │   │   ├── auth.interceptor.ts
    │   │   │   │   ├── cache.interceptor.ts
    │   │   │   │   ├── offline.interceptor.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── services/
    │   │   │   │   ├── auth.service.ts
    │   │   │   │   ├── network.service.ts
    │   │   │   │   ├── notification.service.ts
    │   │   │   │   ├── storage.service.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── models/
    │   │   │   │   ├── user.model.ts
    │   │   │   │   ├── api-response.model.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── constants/
    │   │   │   │   ├── app.constants.ts
    │   │   │   │   ├── storage.constants.ts
    │   │   │   │   └── index.ts
    │   │   │   └── core.module.ts
    │   │   ├── shared/
    │   │   │   ├── components/
    │   │   │   │   ├── header/
    │   │   │   │   │   ├── header.component.ts
    │   │   │   │   │   ├── header.component.html
    │   │   │   │   │   ├── header.component.scss
    │   │   │   │   │   └── header.component.spec.ts
    │   │   │   │   ├── footer/
    │   │   │   │   │   ├── footer.component.ts
    │   │   │   │   │   ├── footer.component.html
    │   │   │   │   │   ├── footer.component.scss
    │   │   │   │   │   └── footer.component.spec.ts
    │   │   │   │   ├── loader/
    │   │   │   │   │   ├── loader.component.ts
    │   │   │   │   │   ├── loader.component.html
    │   │   │   │   │   ├── loader.component.scss
    │   │   │   │   │   └── loader.component.spec.ts
    │   │   │   │   ├── offline-indicator/
    │   │   │   │   │   ├── offline-indicator.component.ts
    │   │   │   │   │   ├── offline-indicator.component.html
    │   │   │   │   │   ├── offline-indicator.component.scss
    │   │   │   │   │   └── offline-indicator.component.spec.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── pipes/
    │   │   │   │   ├── safe-html.pipe.ts
    │   │   │   │   ├── duration.pipe.ts
    │   │   │   │   ├── file-size.pipe.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── directives/
    │   │   │   │   ├── offline-content.directive.ts
    │   │   │   │   ├── lazy-load.directive.ts
    │   │   │   │   └── index.ts
    │   │   │   ├── validators/
    │   │   │   │   ├── custom.validators.ts
    │   │   │   │   └── index.ts
    │   │   │   └── shared.module.ts
    │   │   ├── features/
    │   │   │   ├── content/
    │   │   │   │   ├── components/
    │   │   │   │   │   ├── content-list/
    │   │   │   │   │   │   ├── content-list.component.ts
    │   │   │   │   │   │   ├── content-list.component.html
    │   │   │   │   │   │   ├── content-list.component.scss
    │   │   │   │   │   │   └── content-list.component.spec.ts
    │   │   │   │   │   ├── content-detail/
    │   │   │   │   │   │   ├── content-detail.component.ts
    │   │   │   │   │   │   ├── content-detail.component.html
    │   │   │   │   │   │   ├── content-detail.component.scss
    │   │   │   │   │   │   └── content-detail.component.spec.ts
    │   │   │   │   │   ├── content-player/
    │   │   │   │   │   │   ├── content-player.component.ts
    │   │   │   │   │   │   ├── content-player.component.html
    │   │   │   │   │   │   ├── content-player.component.scss
    │   │   │   │   │   │   └── content-player.component.spec.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── services/
    │   │   │   │   │   ├── content.service.ts
    │   │   │   │   │   ├── content-cache.service.ts
    │   │   │   │   │   ├── content-sync.service.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── models/
    │   │   │   │   │   ├── content.model.ts
    │   │   │   │   │   ├── content-chunk.model.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── resolvers/
    │   │   │   │   │   ├── content.resolver.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── content-routing.module.ts
    │   │   │   │   └── content.module.ts
    │   │   │   ├── offline/
    │   │   │   │   ├── components/
    │   │   │   │   │   ├── offline-manager/
    │   │   │   │   │   │   ├── offline-manager.component.ts
    │   │   │   │   │   │   ├── offline-manager.component.html
    │   │   │   │   │   │   ├── offline-manager.component.scss
    │   │   │   │   │   │   └── offline-manager.component.spec.ts
    │   │   │   │   │   ├── download-progress/
    │   │   │   │   │   │   ├── download-progress.component.ts
    │   │   │   │   │   │   ├── download-progress.component.html
    │   │   │   │   │   │   ├── download-progress.component.scss
    │   │   │   │   │   │   └── download-progress.component.spec.ts
    │   │   │   │   │   ├── storage-usage/
    │   │   │   │   │   │   ├── storage-usage.component.ts
    │   │   │   │   │   │   ├── storage-usage.component.html
    │   │   │   │   │   │   ├── storage-usage.component.scss
    │   │   │   │   │   │   └── storage-usage.component.spec.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── services/
    │   │   │   │   │   ├── offline-storage.service.ts
    │   │   │   │   │   ├── sync-queue.service.ts
    │   │   │   │   │   ├── chunk-manager.service.ts
    │   │   │   │   │   ├── indexeddb.service.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── workers/
    │   │   │   │   │   ├── chunk-processor.worker.ts
    │   │   │   │   │   ├── content-indexer.worker.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── models/
    │   │   │   │   │   ├── offline-content.model.ts
    │   │   │   │   │   ├── sync-item.model.ts
    │   │   │   │   │   ├── storage-info.model.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── offline-routing.module.ts
    │   │   │   │   └── offline.module.ts
    │   │   │   ├── user/
    │   │   │   │   ├── components/
    │   │   │   │   │   ├── profile/
    │   │   │   │   │   ├── settings/
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── services/
    │   │   │   │   │   ├── user.service.ts
    │   │   │   │   │   ├── preferences.service.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── models/
    │   │   │   │   │   ├── user-profile.model.ts
    │   │   │   │   │   ├── user-preferences.model.ts
    │   │   │   │   │   └── index.ts
    │   │   │   │   ├── user-routing.module.ts
    │   │   │   │   └── user.module.ts
    │   │   │   └── search/
    │   │   │       ├── components/
    │   │   │       │   ├── search-bar/
    │   │   │       │   ├── search-results/
    │   │   │       │   ├── offline-search/
    │   │   │       │   └── index.ts
    │   │   │       ├── services/
    │   │   │       │   ├── search.service.ts
    │   │   │       │   ├── offline-search.service.ts
    │   │   │       │   └── index.ts
    │   │   │       ├── models/
    │   │   │       │   ├── search-result.model.ts
    │   │   │       │   └── index.ts
    │   │   │       ├── search-routing.module.ts
    │   │   │       └── search.module.ts
    │   │   ├── layout/
    │   │   │   ├── main-layout/
    │   │   │   │   ├── main-layout.component.ts
    │   │   │   │   ├── main-layout.component.html
    │   │   │   │   ├── main-layout.component.scss
    │   │   │   │   └── main-layout.component.spec.ts
    │   │   │   ├── sidebar/
    │   │   │   │   ├── sidebar.component.ts
    │   │   │   │   ├── sidebar.component.html
    │   │   │   │   ├── sidebar.component.scss
    │   │   │   │   └── sidebar.component.spec.ts
    │   │   │   └── index.ts
    │   │   ├── app-routing.module.ts
    │   │   ├── app.component.ts
    │   │   ├── app.component.html
    │   │   ├── app.component.scss
    │   │   ├── app.component.spec.ts
    │   │   └── app.module.ts
    │   ├── assets/
    │   │   ├── icons/
    │   │   │   ├── icon-72x72.png
    │   │   │   ├── icon-96x96.png
    │   │   │   ├── icon-128x128.png
    │   │   │   ├── icon-144x144.png
    │   │   │   ├── icon-152x152.png
    │   │   │   ├── icon-192x192.png
    │   │   │   ├── icon-384x384.png
    │   │   │   └── icon-512x512.png
    │   │   ├── images/
    │   │   │   ├── logo.svg
    │   │   │   ├── placeholder.jpg
    │   │   │   └── backgrounds/
    │   │   ├── fonts/
    │   │   │   └── custom-fonts.woff2
    │   │   ├── audio/
    │   │   │   └── notification.mp3
    │   │   └── data/
    │   │       └── fallback-content.json
    │   ├── environments/
    │   │   ├── environment.ts
    │   │   ├── environment.prod.ts
    │   │   └── environment.staging.ts
    │   ├── styles/
    │   │   ├── _variables.scss
    │   │   ├── _mixins.scss
    │   │   ├── _base.scss
    │   │   ├── _components.scss
    │   │   ├── _utilities.scss
    │   │   ├── _responsive.scss
    │   │   └── styles.scss
    │   ├── sw/
    │   │   ├── sw-master.js
    │   │   ├── sw-cache.js
    │   │   ├── sw-sync.js
    │   │   └── sw-update.js
    │   ├── index.html
    │   ├── main.ts
    │   ├── polyfills.ts
    │   ├── test.ts
    │   ├── manifest.json
    │   ├── ngsw-config.json
    │   └── favicon.ico
    ├── public/
    │   ├── robots.txt
    │   └── sitemap.xml
    ├── docs/
    │   ├── architecture.md
    │   ├── api.md
    │   ├── deployment.md
    │   └── offline-features.md
    ├── scripts/
    │   ├── build.sh
    │   ├── deploy.sh
    │   └── generate-icons.js
    ├── e2e/
    │   ├── src/
    │   │   ├── app.e2e-spec.ts
    │   │   ├── app.po.ts
    │   │   └── offline.e2e-spec.ts
    │   ├── protractor.conf.js
    │   └── tsconfig.json
    ├── .vscode/
    │   ├── settings.json
    │   ├── launch.json
    │   └── extensions.json
    ├── .github/
    │   └── workflows/
    │       ├── ci.yml
    │       └── deploy.yml
    ├── angular.json
    ├── package.json
    ├── package-lock.json
    ├── tsconfig.json
    ├── tsconfig.app.json
    ├── tsconfig.spec.json
    ├── karma.conf.js
    ├── .eslintrc.json
    ├── .prettierrc
    ├── .gitignore
    ├── README.md
    └── CHANGELOG.md
    

Key Features of This Structure:
-------------------------------

### 1. **Core Module**

*   Guards for authentication and offline functionality
*   Interceptors for caching and offline handling
*   Core services and models
*   Application constants

### 2. **Shared Module**

*   Reusable components (header, footer, offline indicator)
*   Custom pipes and directives
*   Common validators
*   Shared functionality across features

### 3. **Feature Modules**

*   **Content**: Main spiritual content management
*   **Offline**: PWA and offline functionality
*   **User**: User management and preferences
*   **Search**: Online and offline search capabilities

### 4. **PWA Specific Files**

*   Service worker files in `src/sw/`
*   PWA manifest and configuration
*   Icons for different device sizes

### 5. **Offline Services**

*   IndexedDB service for data management
*   Chunk manager for video/audio splitting
*   Sync queue for offline actions
*   Web Workers for background processing

### 6. **Development Tools**

*   E2E tests including offline scenarios
*   Build and deployment scripts
*   VS Code configuration
*   GitHub Actions workflows
This structure provides clear separation of concerns, scalability, and maintainability while supporting comprehensive PWA and offline capabilities.