<div class="landing-container">
  <!-- 1. 大图占满整行 -->
  <div class="hero-section">
    <p-image
      src="assets/images/landing.png"
      alt="HolyBless Hero Banner"
      width="100%"
      height="400"
      [preview]="false"
      styleClass="hero-image"
    >
    </p-image>
    <div class="hero-overlay">
      <div class="hero-content">
        <h1 class="hero-title">欢迎来到 HolyBless</h1>
        <p class="hero-subtitle">您的数字资源管理平台</p>
      </div>
    </div>
  </div>

  <!-- 2. Collection Name: Miracle (两行三列Grid) -->
  <div class="section-container">
    <div class="collection-header">
      <h2 class="collection-title">Miracle Collection</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <p-card
        *ngFor="let miracle of miracleItems"
        [header]="miracle.title"
        styleClass="miracle-card"
      >
        <p class="card-description">{{ miracle.description }}</p>
        <p-tag
          [value]="miracle.creationTime"
          severity="info"
          styleClass="creation-time-tag"
        >
        </p-tag>
      </p-card>
    </div>
  </div>

  <!-- 3. Lecture轮播图 和 Tianmenkai Grid 在一行 -->
  <div class="section-container">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Lecture 轮播图 -->
      <div>
        <div class="collection-header">
          <h3 class="collection-title">Lecture Collection</h3>
        </div>
        <p-galleria
          [value]="lectureItems"
          [responsiveOptions]="galleriaResponsiveOptions"
          [containerStyle]="{ 'max-width': '100%' }"
          [numVisible]="1"
          [circular]="true"
          [autoPlay]="true"
          [transitionInterval]="3000"
          [showThumbnails]="false"
          [showIndicators]="true"
          [showItemNavigators]="true"
          styleClass="lecture-galleria"
        >
          <ng-template pTemplate="item" let-lecture>
            <div class="galleria-item">
              <img
                [src]="lecture.cover"
                [alt]="lecture.title"
                class="galleria-image"
              />
              <div class="galleria-caption">
                <h4>{{ lecture.title }}</h4>
              </div>
            </div>
          </ng-template>
        </p-galleria>
      </div>

      <!-- Tianmenkai Grid -->
      <div>
        <div class="collection-header">
          <h3 class="collection-title">Tianmenkai Collection</h3>
        </div>
        <div class="grid grid-cols-3 gap-3">
          <div *ngFor="let item of tianmenkaiItems" class="tianmenkai-item">
            <img
              [src]="item.cover"
              [alt]="item.title"
              class="tianmenkai-cover"
            />
            <h5 class="tianmenkai-title">{{ item.title }}</h5>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 4. Top榜单 和 Notice榜单 -->
  <div class="section-container">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 equal-height-cards">
      <!-- Top榜单 -->
      <div class="card-wrapper">
        <div class="collection-header">
          <h3 class="collection-title">Top Collection</h3>
        </div>
        <p-card>
          <div class="ranking-list">
            <div
              *ngFor="let item of topItems; let i = index"
              class="ranking-item"
            >
              <span class="rank-number">{{ i + 1 }}</span>
              <span class="rank-title">{{ item.title }}</span>
              <span class="rank-score">{{ item.score }}</span>
            </div>
          </div>
        </p-card>
      </div>

      <!-- Notice榜单 -->
      <div class="card-wrapper">
        <div class="collection-header">
          <h3 class="collection-title">Notice Collection</h3>
        </div>
        <p-card>
          <div class="notice-list">
            <div *ngFor="let notice of noticeItems" class="notice-item">
              <p-tag
                [value]="notice.type"
                [severity]="notice.severity"
                styleClass="notice-tag"
              >
              </p-tag>
              <span class="notice-title">{{ notice.title }}</span>
              <span class="notice-date">{{ notice.date }}</span>
            </div>
          </div>
        </p-card>
      </div>
    </div>
  </div>

  <!-- 5. 固定文本内容 -->
  <div class="section-container">
    <p-divider></p-divider>
    <div class="fixed-content">
      <h2 class="content-title">关于 HolyBless</h2>
      <p class="content-text">
        HolyBless
        是一个综合性的数字资源管理平台，为用户提供电子书阅读、云端存储、播客收听等多种功能。
        我们致力于为用户创造最佳的数字化体验，让知识和娱乐触手可及。
      </p>
      <p class="content-text">
        通过先进的技术和用户友好的界面设计，HolyBless
        让您能够轻松管理和享受您的数字内容。
        无论是工作还是娱乐，我们都是您最好的数字伙伴。
      </p>
    </div>
  </div>

  <!-- 6. Footer -->
  <footer class="footer-section">
    <div
      class="flex flex-col md:flex-row items-center justify-center py-6 bg-gray-100"
    >
      <div
        class="text-gray-500 w-full md:w-[32rem] text-wrap leading-7 text-center md:text-left"
      >
        声明
        <br />
        圣光临在网站仅供个人自主学习使用，目前只设立本站，未设立任何自媒体网络平台账号，未设立任何社群。网络上所有使用圣光临在近似名、头像发布相关内容，都是与本站无关的私人行为，敬请注意。
      </div>
      <div class="flex flex-col items-center mt-6 md:mt-0 md:ml-32">
        <img src="assets/images/invite.png" class="w-24" alt="" srcset="" />
        APP下载
      </div>
    </div>
  </footer>
</div>
