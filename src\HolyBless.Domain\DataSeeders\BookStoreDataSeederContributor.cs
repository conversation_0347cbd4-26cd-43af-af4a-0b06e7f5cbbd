﻿using System;
using System.Threading.Tasks;
using HolyBless.Books;
using HolyBless.Entities.Books;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders;

public class BookStoreDataSeederContributor
    : IDataSeedContributor, ITransientDependency
{
    private readonly IRepository<EBook, int> _bookRepository;

    public BookStoreDataSeederContributor(IRepository<EBook, int> bookRepository)
    {
        _bookRepository = bookRepository;
    }

    public async Task SeedAsync(DataSeedContext context)
    {
        if (await _bookRepository.GetCountAsync() <= 0)
        {
            //TODO: add seeding code
        }
    }
}