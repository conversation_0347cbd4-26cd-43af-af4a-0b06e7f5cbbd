import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerModule } from 'primeng/drawer';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { DrawerService } from '../../services/drawer.service';

@Component({
  selector: 'app-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    DividerModule,
    ButtonModule,
  ],
  templateUrl: './drawer.component.html',
  styleUrls: ['./drawer.component.scss']
})
export class DrawerComponent {
  // 注入 DrawerService
  public drawerService = inject(DrawerService);
}