﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class ContentTypetoContentCategory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ContentType",
                table: "BucketFiles",
                newName: "Views");

            migrationBuilder.AddColumn<int>(
                name: "ContentCategory",
                table: "BucketFiles",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContentCategory",
                table: "BucketFiles");

            migrationBuilder.RenameColumn(
                name: "Views",
                table: "BucketFiles",
                newName: "ContentType");
        }
    }
}
