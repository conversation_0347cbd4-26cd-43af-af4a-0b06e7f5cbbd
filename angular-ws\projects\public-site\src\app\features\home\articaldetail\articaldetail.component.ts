import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-articaldetail',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="articaldetail-container p-6 max-w-4xl mx-auto">
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- 文章头部 -->
        <div class="p-6 border-b">
          <h1 class="text-3xl font-bold mb-4">文章标题示例</h1>
          <div class="flex items-center text-sm text-gray-500 mb-4">
            <span class="mr-6">📅 发布时间: 2024-01-01</span>
            <span class="mr-6">👤 作者: 示例作者</span>
            <span>👁️ 阅读量: 1,234</span>
          </div>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">标签1</span>
            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">标签2</span>
          </div>
        </div>
        
        <!-- 文章内容 -->
        <div class="p-6">
          <div class="prose max-w-none">
            <p class="text-gray-700 leading-relaxed mb-4">
              这是文章的第一段内容。在这里可以展示文章的主要内容，支持富文本格式，包括段落、标题、列表等各种格式。
            </p>
            <p class="text-gray-700 leading-relaxed mb-4">
              这是文章的第二段内容。可以继续添加更多的段落来展示完整的文章内容。
            </p>
            <h2 class="text-xl font-semibold mt-6 mb-3">子标题示例</h2>
            <p class="text-gray-700 leading-relaxed mb-4">
              在子标题下的内容，可以进一步细化文章的结构和内容。
            </p>
          </div>
        </div>
        
        <!-- 文章底部 -->
        <div class="p-6 border-t bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                <span>👍</span>
                <span>点赞</span>
              </button>
              <button class="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                <span>💬</span>
                <span>评论</span>
              </button>
            </div>
            <button class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
              分享
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .articaldetail-container {
      min-height: 400px;
    }
    .prose {
      color: #374151;
      line-height: 1.75;
    }
  `]
})
export class ArticaldetailComponent {
  constructor() { }
}
