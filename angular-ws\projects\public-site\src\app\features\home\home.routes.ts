import { Routes } from '@angular/router';

export const HOME_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./home.component').then(m => m.HomeComponent),
    title: 'HolyBless - 主站',
    children: [
      {
        path: 'withcoverlist',
        loadComponent: () => import('./withcoverlist/withcoverlist.component').then(m => m.WithcoverlistComponent),
        title: 'HolyBless - 带封面列表'
      },
      {
        path: 'withoutcoverlist',
        loadComponent: () => import('./withoutcoverlist/withoutcoverlist.component').then(m => m.WithoutcoverlistComponent),
        title: 'HolyBless - 无封面列表'
      },
      {
        path: 'articaldetail',
        loadComponent: () => import('./articaldetail/articaldetail.component').then(m => m.ArticaldetailComponent),
        title: 'HolyBless - 文章详情'
      },
      {
        path: 'pavelistcontent',
        loadComponent: () => import('./pavelistcontent/pavelistcontent.component').then(m => m.<PERSON>contentComponent),
        title: 'HolyBless - 平铺列表内容'
      },
      {
        path: 'collectiontree',
        loadComponent: () => import('./collectiontree/collectiontree.component').then(m => m.CollectiontreeComponent),
        title: 'HolyBless - 收藏树'
      }
    ]
  }
];
