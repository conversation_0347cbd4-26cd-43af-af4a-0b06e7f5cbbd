import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ButtonModule} from 'primeng/button';

@Component({
    selector: 'hero-widget',
    standalone: true,
    imports: [CommonModule, ButtonModule],
    template: `
        <div id="home" #home class="landing-banner px-6 md:px-20 py-20 lg:flex flex-wrap">
            <div class="banner-left w-full lg:w-8/12">
                <span class="block font-normal mb-12 text-6xl sm:text-7xl xl:text-8xl text-surface-900 dark:text-surface-0 w-full">Start Your Business With The All-In-One <b class="text-primary font-medium">FREYA</b> Dashboard Template!</span>
                <h4 class="font-normal text-surface-900 dark:text-surface-0 m-0 mb-12 leading-normal w-10/12" style="letter-spacing: 0.6px">
                    Say goodbye to clunky, outdated dashboards and hello to modern, user-friendly design. With an array of pages and components at your fingertips, building your business dashboard has never been easier. Start streamlining your
                    operations and making data-driven decisions with FREYA today!
                </h4>
                <p-button label="Learn More" icon="pi pi-arrow-right" size="large" styleClass="mb-12"></p-button>

                <div class="logo-section relative w-full md:w-8/12 mt-12 users-container">
                    <div class="fade-left h-24 w-24 block absolute top-0 left-0 z-20"></div>
                    <div class="marquee-wrapper overflow-hidden flex">
                        @for (i of [1, 2, 3]; track i) {
                            <div class="marquee">
                                @for (user of usersData; track user) {
                                    <div class="w-full">
                                        <img src="/images/landing/logocloud/{{ user }}-500.svg" alt="user" />
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    <div class="fade-right h-24 w-24 block absolute top-0 right-0 z-20"></div>
                </div>
            </div>
            <div class="banner-right w-4/12 hidden lg:block">
                <div id="ex1" class="cards w-full flex flex-col items-end mt-20 pl-4">
                    <svg id="ex1-layer" class="w-full lg:mr-0" viewBox="0 0 261 311" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M212.774 11.5565C212.161 11.2497 211.274 10.9089 210.251 10.5681C208.751 10.091 206.841 9.75017 204.863 9.57977C204.727 7.46675 204.386 5.42189 203.943 3.78601C203.636 2.66134 203.295 1.70707 203.022 1.05954C202.749 0.411999 202.545 0.0371094 202.545 0.0371094C202.545 0.0371094 202.34 0.411999 202.067 1.05954C201.795 1.70707 201.454 2.66134 201.147 3.78601C200.703 5.42189 200.397 7.43267 200.226 9.57977C198.248 9.75017 196.373 10.091 194.839 10.5681C193.781 10.9089 192.929 11.2497 192.315 11.5565C191.701 11.8291 191.36 12.0677 191.36 12.0677C191.36 12.0677 191.701 12.3062 192.315 12.5789C192.929 12.8515 193.816 13.2264 194.839 13.5672C196.339 14.0444 198.248 14.3852 200.226 14.5556C200.362 16.7027 200.703 18.7135 201.147 20.3493C201.454 21.474 201.795 22.4283 202.067 23.0758C202.34 23.7233 202.545 24.0982 202.545 24.0982C202.545 24.0982 202.749 23.7233 203.022 23.0758C203.295 22.4283 203.636 21.474 203.943 20.3493C204.386 18.7135 204.693 16.7027 204.863 14.5556C206.841 14.3852 208.717 14.0444 210.251 13.5672C211.308 13.2264 212.161 12.8856 212.774 12.5789C213.388 12.3062 213.729 12.0677 213.729 12.0677C213.729 12.0677 213.388 11.8632 212.774 11.5565ZM214.504 198.797C204.683 192.083 194.658 192.117 187.634 193.48C181.428 179.166 173.995 166.454 167.107 155.412C162.094 147.369 155.888 138.031 147.978 129.954C140.919 122.762 132.77 116.321 123.7 110.902C105.764 100.099 86.6347 95.8726 66.8577 98.3265L65.221 98.531L64.8118 100.133C64.2662 102.348 63.4478 106.37 63.0387 109.301C62.3226 114.753 62.0839 120.445 62.3567 126.239C62.8682 136.736 65.2551 147.028 69.4151 156.809C73.5751 166.488 78.9967 175.588 85.6118 183.835C92.636 192.628 100.99 200.058 108.219 206.158C120.358 216.383 132.224 224.835 144.159 231.821C143.272 234.48 142.556 237.615 142.351 241.057C141.976 247.294 143.238 253.326 146.136 258.984C150.637 267.777 164.379 278.342 169.596 278.308C170.824 278.308 171.369 277.728 171.642 277.251C172.051 276.433 172.29 275.479 172.358 274.388C172.596 270.55 170.53 265.831 168.862 262.021L168.846 261.983C167.891 259.836 167.073 257.961 166.868 256.735C166.8 256.292 166.766 255.78 166.8 255.303C167.005 251.997 169.528 248.351 172.324 245.317C172.5 245.381 172.675 245.444 172.85 245.507C173.997 245.92 175.126 246.326 176.279 246.681C177.643 247.124 179.041 247.567 180.405 247.976C180.814 248.112 181.258 248.214 181.701 248.317C182.417 248.487 183.099 248.657 183.645 248.896C185.418 249.612 186.952 248.726 188.146 247.771C196.568 254.826 204.649 260.586 206.695 258.779C207.002 258.507 207.207 258.064 207.241 257.518C207.411 254.553 203.217 246.987 198.068 238.944C198.852 238.229 199.807 237.206 199.909 235.604C199.909 235.264 199.909 234.889 199.841 234.514C199.603 233.151 199.33 231.821 199.057 230.526C198.852 229.538 198.648 228.55 198.477 227.561C198.205 226.096 197.898 224.63 197.557 223.131C201.444 220.37 206.081 218.019 209.662 219.075C210.782 219.395 212.382 220.345 214.238 221.447L214.606 221.665L214.69 221.715C219.313 224.468 225.066 227.893 229.405 226.334C229.95 226.13 230.666 225.585 230.734 224.187C231.075 218.802 222.38 204.216 214.435 198.797H214.504ZM122.984 211.85C140.067 199.547 156.025 185.54 170.38 170.101C170.568 170.442 170.764 170.782 170.96 171.123C171.156 171.464 171.352 171.805 171.54 172.146C157.696 187.312 141.669 201.455 124.859 213.281L123.018 211.85H122.984ZM113.402 110.868L115.55 111.891C99.9672 123.342 85.3731 136.224 72.0747 150.266C71.9998 150.069 71.9223 149.87 71.8443 149.67L71.8442 149.669L71.8439 149.669C71.6382 149.14 71.4294 148.603 71.2564 148.085C83.8728 134.486 98.3305 121.74 113.402 110.868ZM67.1646 126.034C66.8918 120.547 67.0964 115.128 67.8124 109.948C68.0852 107.835 68.6308 105.006 69.1082 102.893C82.7134 101.462 95.9777 103.405 108.73 108.687C94.8524 118.979 81.7928 130.499 69.8583 142.972C68.4262 137.417 67.4715 131.76 67.1987 126L67.1646 126.034ZM89.3626 180.905C83.0885 173.066 77.9396 164.443 73.9842 155.276C87.7941 140.348 103.65 126.341 120.188 114.481C120.528 114.685 120.869 114.856 121.21 115.026L121.21 115.026C129.906 120.241 137.748 126.409 144.534 133.328C152.138 141.064 158.139 150.129 163.049 157.968C164.515 160.32 166.05 162.808 167.55 165.33C153.024 181.245 136.214 196.071 118.619 208.442C116.198 206.533 113.811 204.591 111.356 202.546C104.298 196.616 96.1823 189.391 89.3967 180.905H89.3626ZM193.772 228.413C193.885 229.095 194.029 229.777 194.173 230.458C194.245 230.799 194.317 231.14 194.386 231.481C194.59 232.401 194.761 233.287 194.897 234.173C189.373 225.959 183.508 218.189 181.019 215.326C175.154 208.715 167.209 206.124 163.288 209.601C161.992 210.725 161.242 212.429 161.14 214.474C160.901 218.53 163.049 223.642 166.8 227.902C168.675 230.015 175.87 237.07 183.679 243.92C183.516 243.879 183.353 243.827 183.197 243.776L183.197 243.776C183.092 243.742 182.99 243.709 182.894 243.682C182.769 243.647 182.644 243.617 182.519 243.587C182.269 243.526 182.019 243.466 181.769 243.375C180.439 243 179.109 242.591 177.814 242.148C173.074 240.58 168.232 238.672 163.083 236.286C151.729 231.038 140.544 224.46 129.224 216.383C145.42 204.727 160.526 191.401 174.165 176.781C180.098 187.687 185.725 199.751 189.85 212.975C191.726 219.007 192.953 223.744 193.806 228.345L193.772 228.413ZM139.76 183.052C145.761 177.769 149.342 170.442 149.819 162.467C150.33 154.492 147.671 146.79 142.386 140.791C131.474 128.42 112.515 127.227 100.138 138.167C94.1364 143.45 90.556 150.743 90.0787 158.752C89.5672 166.727 92.2269 174.429 97.5121 180.427C102.797 186.426 110.128 190.004 118.108 190.515C126.087 191.027 133.793 188.368 139.794 183.086L139.76 183.052ZM94.8524 159.025C95.2616 152.311 98.2623 146.176 103.309 141.746C113.709 132.578 129.633 133.566 138.805 143.961C143.238 149.005 145.454 155.446 145.045 162.16C144.636 168.874 141.635 175.009 136.589 179.439C131.542 183.87 125.098 186.085 118.38 185.676C111.663 185.267 105.525 182.268 101.092 177.224C96.6596 172.18 94.4433 165.739 94.8524 159.025ZM132.838 175.179C140.919 168.056 141.669 155.753 134.543 147.71C127.416 139.667 115.107 138.883 107.06 146.006C99.0124 153.129 98.2282 165.432 105.355 173.475C112.481 181.518 124.791 182.302 132.838 175.179ZM108.73 158.759L113.145 154.695L115.735 157.509L122.368 148.473L125.754 152.152L119.121 161.188L120.249 162.414L126.882 153.378L130.268 157.057L123.635 166.093L126.689 169.412L122.274 173.475L108.73 158.759ZM91.2477 296.507C92.3048 296.848 93.1572 297.189 93.771 297.495C94.3848 297.802 94.7258 298.007 94.7258 298.007C94.7258 298.007 94.3848 298.245 93.771 298.518C93.1572 298.824 92.3048 299.165 91.2477 299.506C89.7133 299.983 87.8379 300.358 85.8602 300.494C85.6897 302.642 85.3828 304.652 84.9395 306.288C84.6326 307.413 84.2917 308.367 84.0189 309.015C83.7461 309.662 83.5415 310.037 83.5415 310.037C83.5415 310.037 83.3369 309.662 83.0641 309.015C82.7913 308.367 82.4503 307.413 82.1435 306.288C81.7002 304.652 81.3592 302.607 81.2228 300.494C79.2451 300.324 77.3356 299.983 75.8353 299.506C74.8123 299.165 73.9258 298.824 73.312 298.518C72.6982 298.245 72.3572 298.007 72.3572 298.007C72.3572 298.007 72.6982 297.768 73.312 297.495C73.9258 297.189 74.7782 296.848 75.8353 296.507C77.3697 296.03 79.2451 295.689 81.2228 295.519C81.3592 293.372 81.7002 291.361 82.1435 289.725C82.4503 288.6 82.7913 287.646 83.0641 286.998C83.3369 286.351 83.5415 285.976 83.5415 285.976C83.5415 285.976 83.7461 286.351 84.0189 286.998C84.2917 287.646 84.6326 288.6 84.9395 289.725C85.3828 291.361 85.7238 293.372 85.8602 295.519C87.8379 295.689 89.7474 296.03 91.2477 296.507ZM22.0621 191.095C21.4484 190.788 20.5959 190.447 19.5389 190.106C18.0385 189.629 16.129 189.288 14.1513 189.118C14.0149 186.971 13.6739 184.96 13.2307 183.324C12.9238 182.2 12.5828 181.245 12.31 180.598C12.0372 179.95 11.8326 179.575 11.8326 179.575C11.8326 179.575 11.628 179.95 11.3553 180.598C11.0825 181.245 10.7415 182.2 10.4346 183.324C9.99133 184.96 9.65034 186.971 9.51395 189.118C7.53625 189.288 5.66084 189.629 4.12641 190.106C3.06936 190.447 2.2169 190.788 1.60313 191.095C0.98936 191.367 0.648376 191.606 0.648376 191.606C0.648376 191.606 0.98936 191.845 1.60313 192.117C2.2169 192.424 3.10346 192.765 4.12641 193.105C5.62674 193.583 7.53625 193.923 9.51395 194.094C9.65034 196.207 9.99133 198.252 10.4346 199.888C10.7415 201.012 11.0825 201.967 11.3553 202.614C11.628 203.262 11.8326 203.637 11.8326 203.637C11.8326 203.637 12.0372 203.262 12.31 202.614C12.5828 201.967 12.9238 201.012 13.2307 199.888C13.6739 198.252 13.9808 196.241 14.1513 194.094C16.129 193.958 18.0044 193.583 19.5389 193.105C20.5959 192.765 21.4484 192.424 22.0621 192.117C22.6759 191.845 23.0169 191.606 23.0169 191.606C23.0169 191.606 22.6759 191.401 22.0621 191.095ZM238.45 153.572C239.507 153.912 240.36 154.253 240.974 154.56C241.587 154.867 241.928 155.071 241.928 155.071C241.928 155.071 241.587 155.31 240.974 155.582C240.36 155.889 239.507 156.23 238.45 156.571C236.916 157.048 235.041 157.423 233.063 157.559C232.892 159.706 232.585 161.717 232.142 163.353C231.835 164.478 231.494 165.432 231.221 166.079C230.949 166.727 230.744 167.102 230.744 167.102C230.744 167.102 230.54 166.727 230.267 166.079C229.994 165.432 229.653 164.478 229.346 163.353C228.903 161.717 228.562 159.672 228.425 157.559C226.448 157.389 224.538 157.048 223.038 156.571C222.015 156.23 221.128 155.889 220.515 155.582C219.901 155.31 219.56 155.071 219.56 155.071C219.56 155.071 219.901 154.833 220.515 154.56C221.128 154.253 221.981 153.912 223.038 153.572C224.572 153.094 226.448 152.754 228.425 152.583C228.562 150.436 228.903 148.425 229.346 146.79C229.653 145.665 229.994 144.711 230.267 144.063C230.54 143.416 230.744 143.041 230.744 143.041C230.744 143.041 230.949 143.416 231.221 144.063C231.494 144.711 231.835 145.665 232.142 146.79C232.585 148.425 232.926 150.436 233.063 152.583C235.041 152.754 236.95 153.094 238.45 153.572ZM119.277 44.4445C120.3 44.7853 121.186 45.1262 121.8 45.4329C122.414 45.7396 122.755 45.9441 122.755 45.9441C122.755 45.9441 122.414 46.1827 121.8 46.4553C121.186 46.762 120.334 47.1029 119.277 47.4437C117.742 47.9208 115.867 48.2616 113.889 48.432C113.719 50.5791 113.412 52.5899 112.968 54.2258C112.661 55.3504 112.321 56.3047 112.048 56.9522C111.775 57.5998 111.57 57.9747 111.57 57.9747C111.57 57.9747 111.366 57.5998 111.093 56.9522C110.82 56.3047 110.479 55.3504 110.172 54.2258C109.729 52.5899 109.388 50.545 109.252 48.432C107.274 48.2616 105.364 47.9208 103.864 47.4437C102.807 47.1029 101.955 46.762 101.341 46.4553C100.727 46.1827 100.386 45.9441 100.386 45.9441C100.386 45.9441 100.727 45.7055 101.341 45.4329C101.955 45.1262 102.807 44.7853 103.864 44.4445C105.399 43.9674 107.274 43.6266 109.252 43.4562C109.388 41.3091 109.729 39.2983 110.172 37.6624C110.479 36.5378 110.82 35.5835 111.093 34.936C111.366 34.2884 111.57 33.9135 111.57 33.9135C111.57 33.9135 111.775 34.2884 112.048 34.936C112.321 35.5835 112.661 36.5378 112.968 37.6624C113.412 39.2983 113.753 41.3432 113.889 43.4562C115.867 43.6266 117.742 43.9674 119.277 44.4445ZM185.598 97.3722C185.189 97.1677 184.609 96.9632 183.927 96.7247C182.938 96.4179 181.677 96.1794 180.381 96.0771C180.279 94.6798 180.074 93.3166 179.767 92.2601C179.562 91.5103 179.358 90.8968 179.153 90.4538C178.983 90.0107 178.846 89.7722 178.846 89.7722C178.846 89.7722 178.71 90.0107 178.54 90.4538L178.524 90.493L178.524 90.4934L178.524 90.4937C178.355 90.9334 178.124 91.5326 177.926 92.2601C177.619 93.3506 177.414 94.6798 177.312 96.0771C176.016 96.1794 174.755 96.4179 173.766 96.7247C173.084 96.9632 172.504 97.1677 172.095 97.3722C171.686 97.5767 171.447 97.713 171.447 97.713C171.447 97.713 171.686 97.8493 172.095 98.0538C172.504 98.2583 173.084 98.4969 173.766 98.7014C174.755 99.0081 176.016 99.2467 177.312 99.3489C177.414 100.746 177.619 102.109 177.926 103.166C178.13 103.916 178.335 104.529 178.54 104.972C178.71 105.415 178.846 105.654 178.846 105.654C178.846 105.654 178.983 105.415 179.153 104.972L179.168 104.933L179.168 104.933C179.338 104.493 179.568 103.894 179.767 103.166C180.074 102.075 180.279 100.746 180.381 99.3489C181.677 99.2467 182.938 99.0081 183.927 98.7014C184.609 98.4969 185.189 98.2583 185.598 98.0538C186.007 97.8834 186.246 97.713 186.246 97.713C186.246 97.713 186.007 97.5767 185.598 97.3722ZM258.398 74.4698C259.08 74.7084 259.66 74.9129 260.069 75.1174C260.478 75.3219 260.717 75.4582 260.717 75.4582C260.717 75.4582 260.478 75.6286 260.069 75.799C259.66 76.0035 259.08 76.242 258.398 76.4465C257.409 76.7533 256.147 76.9918 254.852 77.0941C254.749 78.4914 254.545 79.8205 254.238 80.9111C254.039 81.6389 253.809 82.2382 253.639 82.6779L253.639 82.6782L253.624 82.7174C253.454 83.1605 253.317 83.399 253.317 83.399C253.317 83.399 253.181 83.1605 253.01 82.7174C252.806 82.2744 252.601 81.6609 252.397 80.9111C252.09 79.8546 251.885 78.4914 251.783 77.0941C250.487 76.9918 249.225 76.7533 248.237 76.4465C247.555 76.242 246.975 76.0035 246.566 75.799C246.157 75.5945 245.918 75.4582 245.918 75.4582C245.918 75.4582 246.157 75.3219 246.566 75.1174C246.975 74.9129 247.555 74.7084 248.237 74.4698C249.225 74.1631 250.487 73.9245 251.783 73.8223C251.885 72.425 252.09 71.0958 252.397 70.0052C252.595 69.2772 252.826 68.6778 252.995 68.238L252.995 68.2377L253.01 68.1989C253.181 67.7559 253.317 67.5173 253.317 67.5173C253.317 67.5173 253.454 67.7559 253.624 68.1989C253.829 68.642 254.033 69.2554 254.238 70.0052C254.545 71.0617 254.749 72.425 254.852 73.8223C256.147 73.9245 257.409 74.1631 258.398 74.4698ZM76.4149 238.876C76.0057 238.672 75.4261 238.467 74.7441 238.229C73.7553 237.922 72.4936 237.683 71.1979 237.581C71.0956 236.184 70.891 234.821 70.5841 233.764C70.3795 233.014 70.1749 232.401 69.9703 231.958C69.7999 231.515 69.6635 231.276 69.6635 231.276C69.6635 231.276 69.5271 231.515 69.3566 231.958L69.3415 231.997C69.1722 232.437 68.9414 233.036 68.7428 233.764C68.4359 234.855 68.2313 236.184 68.129 237.581C66.8333 237.683 65.5717 237.922 64.5828 238.229C63.9008 238.467 63.3212 238.672 62.912 238.876C62.5028 239.081 62.2641 239.217 62.2641 239.217C62.2641 239.217 62.5028 239.353 62.912 239.558C63.3212 239.762 63.9008 240.001 64.5828 240.205C65.5717 240.512 66.8333 240.751 68.129 240.853C68.2313 242.25 68.4359 243.613 68.7428 244.67C68.9474 245.42 69.152 246.033 69.3566 246.476C69.5271 246.919 69.6635 247.158 69.6635 247.158C69.6635 247.158 69.7999 246.919 69.9703 246.476L69.9855 246.437C70.1548 245.997 70.3855 245.398 70.5841 244.67C70.891 243.579 71.0956 242.25 71.1979 240.853C72.4936 240.751 73.7553 240.512 74.7441 240.205C75.4261 240.001 76.0057 239.762 76.4149 239.558C76.8241 239.387 77.0628 239.217 77.0628 239.217C77.0628 239.217 76.8241 239.081 76.4149 238.876ZM13.1284 266.311C13.8103 266.55 14.39 266.754 14.7992 266.959H14.8333C15.2425 267.163 15.4812 267.3 15.4812 267.3C15.4812 267.3 15.2425 267.436 14.8333 267.64C14.4241 267.845 13.8444 268.049 13.1625 268.288C12.1736 268.595 10.912 268.833 9.61625 268.936C9.51395 270.333 9.30936 271.662 9.00247 272.753C8.80383 273.481 8.57304 274.08 8.40374 274.52L8.3887 274.559C8.21821 275.002 8.08182 275.24 8.08182 275.24C8.08182 275.24 7.94543 275.002 7.77493 274.559C7.57034 274.116 7.36575 273.502 7.16116 272.753C6.85428 271.696 6.64969 270.333 6.54739 268.936C5.25166 268.833 3.99002 268.595 3.00116 268.288C2.3192 268.049 1.73952 267.845 1.33034 267.64C0.921163 267.436 0.682475 267.3 0.682475 267.3C0.682475 267.3 0.921163 267.163 1.33034 266.959C1.73952 266.754 2.3192 266.516 3.00116 266.311C3.99002 266.005 5.25166 265.766 6.54739 265.664C6.64969 264.266 6.85428 262.937 7.16116 261.847C7.35978 261.119 7.59054 260.519 7.75983 260.08L7.77493 260.04C7.94543 259.597 8.08182 259.359 8.08182 259.359C8.08182 259.359 8.21821 259.597 8.3887 260.04C8.5592 260.483 8.76379 261.097 8.96838 261.847C9.27526 262.937 9.47985 264.266 9.58215 265.664C10.8779 265.766 12.1395 266.005 13.1284 266.311Z"
                            fill="var(--p-primary-color)"
                        />
                    </svg>
                </div>
            </div>
        </div>
    `,
    styles: `
        .marquee-wrapper:hover .marquee {
            animation-play-state: paused !important;
        }

        .marquee-wrapper {
            user-select: none;
            gap: 3rem;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }

        .marquee {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: space-around;
            gap: 3rem;
            min-width: 100%;
            animation: scroll 30s linear infinite;
        }

        .marquee > div {
            max-width: clamp(10rem, 1rem + 28vmin, 20rem);
            aspect-ratio: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 8rem;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0%);
            }

            100% {
                transform: translateX(calc(-100% - 3rem));
            }
        }

        @keyframes marquee {
            0% {
                transform: translate3d(0%, 0, 0);
            }

            100% {
                transform: translate3d(-100%, 0, 0);
            }
        }

        .landing-wrapper {
            max-width: 1504px;
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .landing-banner {
            margin-bottom: 12rem;
        }

        @media screen and (max-width: 991px) {
            .landing-wrapper .landing-banner {
                height: auto !important;
                margin-bottom: 8rem;
            }
        }

        @keyframes fadein {
            0% {
                opacity: 0;
            }

            100% {
                opacity: 1;
            }
        }

        .fadein {
            animation: fadein 0.3s linear;
        }

        .fade-right {
            background: linear-gradient(to left, var(--surface-ground), transparent);
        }
        .fade-left {
            background: linear-gradient(to right, var(--surface-ground), transparent);
        }
    `
})
export class HeroWidget implements OnInit {
    usersData = ['alfred', 'bastion', 'charot', 'franki', 'hodly', 'hyper', 'shodan'];

    ngOnInit() {
        this.customHover();
    }

    customHover() {
        const constrain: number = 100;
        const mouseOverContainer: HTMLElement | null = document.getElementById('ex1');
        const ex1Layer: HTMLElement | null = document.getElementById('ex1-layer');

        function transforms(x: number, y: number, el: Element): string {
            const box: DOMRect = el.getBoundingClientRect();
            const calcX: number = -(y - box.y - box.height / 2) / constrain;
            const calcY: number = (x - box.x - box.width / 2) / constrain;

            return 'perspective(100px) ' + 'rotateX(' + calcX + 'deg) ' + 'rotateY(' + calcY + 'deg) ';
        }

        function transformElement(el: HTMLElement, xyEl: [number, number, Element]) {
            el.style.transform = transforms(...xyEl);
        }

        if (mouseOverContainer && ex1Layer) {
            mouseOverContainer.onmousemove = function (e: MouseEvent) {
                const xy: [number, number] = [e.clientX, e.clientY];
                const position: [number, number, Element] = [...xy, ex1Layer];

                window.requestAnimationFrame(function () {
                    transformElement(ex1Layer, position);
                });
            };
            mouseOverContainer.onmouseleave = function (e: MouseEvent) {
                window.requestAnimationFrame(function () {
                    setTimeout(() => {
                        return (ex1Layer.style.transform = 'perspective(100px) ' + 'rotateX(' + 0 + 'deg) ' + 'rotateY(' + 0 + 'deg) ');
                    }, 800);
                });
            };
        }
    }
}
