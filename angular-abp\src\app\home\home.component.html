<div class="row mb-3">
  <div class="col-xl-6 col-12 d-flex">
    <div class="card h-lg-100 w-100 overflow-hidden">
      <div class="card-body">
        <div class="starting-content pe-5">
          @if(!hasLoggedIn){
          <h3>Welcome</h3>
          <p class="lead text-muted mb-2">
            {{ '::LongWelcomeMessage' | abpLocalization }}
          </p>
          <a (click)="login()" class="px-4 btn btn-primary mb-4" role="button">
            <i class="fa fa-sign-in"></i> 
            {{ 'AbpAccount::Login' | abpLocalization }}
          </a>
          }

          <h4>Getting Started</h4>
          <p class=" text-muted mb-2">
            Learn how to create and run a new web application using the application startup
            template.
          </p>
          <a
            href="https://abp.io/docs/latest/getting-started"
            class="btn  btn-primary mb-2"
            target="_blank"
          >
            Getting Started
          </a>

          <h4>Web Application Development Tutorial</h4>
          <p class=" text-muted mb-2">
            Learn how to build an ABP based web application named Acme.BookStore.
          </p>
          <a
            href="https://abp.io/docs/latest/tutorials/book-store/part-01?UI=NG&DB=EF"
            class="btn  btn-primary mb-2"
            target="_blank"
          >
            Explore Tutorial
          </a>

          <h4>Customize Lepton Theme</h4>
          <p class="text-muted mb-2">Learn how to customize LeptonX Theme as you wish.</p>
          <a
            href="https://abp.io/docs/commercial/latest/themes/lepton-x/index"
            class="btn btn-primary soft mb-5 mb-xl-0"
            target="_blank"
          >
            Customize Lepton
          </a>
        </div>
        <img class="card-bg-image" src="assets/images/getting-started/bg-01.png" />
      </div>
    </div>
  </div>
  <div class="col-xl-3 col-md-6 d-flex">
    <div class="row">
      <div class="col-12 d-flex">
        <div class="card overflow-hidden mb-3">
          <div class="card-body d-flex flex-column">
            <div class="abp-support abp-logo mb-2"></div>
            <p class="text-muted mb-2">
              You can check for similar problems and solutions, or open a new topic to discuss your
              specific issue.
            </p>
            <a
              class="btn  btn-primary w-50"
              href="https://abp.io/support/questions"
              target="_blank"
            >
              Visit Support
            </a>
            <img
              style="margin-bottom: -24px"
              class="w-100 mt-auto"
              src="assets/images/getting-started/img-support.png"
            />
          </div>
        </div>
      </div>
      <div class="col-12 d-flex">
        <div class="card h-md-100 overflow-hidden">
          <div class="card-body d-flex flex-column">
            <div class="abp-blog abp-logo mb-2"></div>
            <p class="text-muted mb-2">
              You can find content on .NET development, cross-platform, ASP.NET application
              templates, ABP-related news, and more.
            </p>
            <a
              class="btn  btn-primary w-50"              
              href="https://abp.io/blog" 
              target="_blank"
            >
              Visit Blog
            </a>
            <img
              style="margin-bottom: -24px"
              class="w-100 mt-auto"
              src="assets/images/getting-started/img-blog.png"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-3 col-md-6 d-flex">
    <div class="card h-100 overflow-hidden">
      <div class="card-body">
        <div class="abp-community abp-logo mb-2"></div>
        <p class="lead text-muted">
          A unique community platform for <span class="fw-bold">ABP Lovers!</span>
        </p>
        <p class="text-muted mb-2">
          Explore all ABP users' experiences with the ABP Framework, discover articles and videos on
          how to use ABP, and join raffles for a chance to win surprise gifts!
        </p>
        <a 
          class="btn  btn-primary mb-3" 
          href="https://abp.io/community/"
          target="_blank"
        >
          Join ABP Community
        </a>
      </div>
      <img class="mt-3 w-100" src="assets/images/getting-started/img-community.png" />
    </div>
  </div>
</div>

<div class="row">
  <div class="col-xl-3 col-lg-4">
    <div class="card">
      <div class="card-header">
        <div class="card-title text-body">More from ABP.IO</div>
      </div>
      <div class="card-body pt-0">
        <div>
          <table class="table mb-0">
            <tbody>
              <tr>
                <td class="ps-0 d-flex">
                  <div
                    class="rounded-circle me-2 d-flex justify-content-center align-items-center bg-check-icon"
                  >
                    <i class="fa fa-xs fa-check"></i>
                  </div>
                  <div>Latest Release Logs</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://github.com/abpframework/abp/releases" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <div
                    class="rounded-circle me-2 d-flex justify-content-center align-items-center bg-check-icon"
                  >
                    <i class="fa fa-xs fa-check"></i>
                  </div>
                  <div>Video Courses</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://abp.io/video-courses/essentials" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <div
                    class="rounded-circle me-2 d-flex justify-content-center align-items-center bg-check-icon"
                  >
                    <i class="fa fa-xs fa-check"></i>
                  </div>
                  <div>Samples</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://abp.io/docs/latest/Samples/Index" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <div
                    class="rounded-circle me-2 d-flex justify-content-center align-items-center bg-check-icon"
                  >
                    <i class="fa fa-xs fa-check"></i>
                  </div>
                  <div>Books</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://abp.io/books" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex border-bottom-0">
                  <div
                    class="rounded-circle me-2 d-flex justify-content-center align-items-center bg-check-icon"
                  >
                    <i class="fa fa-xs fa-check"></i>
                  </div>
                  <div>FAQ</div>
                </td>
                <td class="text-end pe-0 opacity-25 border-bottom-0">
                  <a href="https://abp.io/faq" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-6 col-lg-8 d-flex">
    <div class="card">
      <div class="card-body d-flex">
        <div class="row align-items-center">
          <div class="col-md-8">
            <p class="text-muted mb-0">THE OFFICIAL GUIDE</p>
            <h3>Mastering ABP Framework</h3>
            <p class="text-muted">
              Written by the creator of the ABP Framework, this book will help you gain a complete
              understanding of the framework and modern web application development techniques.
            </p>
            <div class="d-md-flex mb-2 mb-md-0">
              <a
                href="https://www.amazon.com/gp/product/B097Z2DM8Q"
                target="_blank"
                class="btn btn-primary soft me-md-2 mb-2 mb-md-0"
              >
                Buy on Amazon US
              </a>
              <a
                href="https://www.packtpub.com/product/mastering-abp-framework/9781801079242"
                target="_blank"
                class="btn btn-primary soft mb-2 mb-md-0"
              >
                Buy on PACKT
              </a>
            </div>
          </div>
          <div class="col">
            <img class="w-100" src="assets/images/getting-started/book.png" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-xl-3 col-lg-4">
    <div class="card">
      <div class="card-header">
        <div class="card-title text-body">Follow us on Social Media</div>
      </div>
      <div class="card-body pt-0">
        <div>
          <table class="table mb-0">
            <tbody>
              <tr>
                <td class="ps-0 d-flex">
                  <div class="logo-x me-2"></div>
                  <div>X.com</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://twitter.com/abpframework" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <img src="assets/images/getting-started/discord.svg" class="me-2" />
                  <div>Discord</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://abp.io/community/discord" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <img src="assets/images/getting-started/stack-overflow.svg" class="me-2" />
                  <div>Stack Overflow</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://stackoverflow.com/questions/tagged/abp" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex">
                  <img src="assets/images/getting-started/youtube.svg" class="me-2" />
                  <div>YouTube</div>
                </td>
                <td class="text-end pe-0 opacity-25">
                  <a href="https://www.youtube.com/@Volosoft" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>
                  </a>
                </td>
              </tr>
              <tr>
                <td class="ps-0 d-flex border-bottom-0">
                  <img src="assets/images/getting-started/instagram.svg" class="me-2" />
                  <div>Instagram</div>
                </td>
                <td class="text-end pe-0 opacity-25 border-bottom-0">
                  <a href="https://www.instagram.com/abpframework/" target="_blank">
                    <i class=" fa fa-xs fa-external-link-alt text-dark"></i>                    
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
