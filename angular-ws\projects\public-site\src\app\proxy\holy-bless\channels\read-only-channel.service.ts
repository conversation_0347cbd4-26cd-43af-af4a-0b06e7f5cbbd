import type { ChannelDto, ChannelSearchDto, ChannelTreeDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyChannelService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllListByLanguage = (languageCode: string = "zh-Hans", config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto[]>({
      method: 'GET',
      url: '/api/app/read-only-channel/list-by-language',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getChannelTree = (languageCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-channel/channel-tree',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: ChannelSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ChannelDto>>({
      method: 'GET',
      url: '/api/app/read-only-channel',
      params: { languageCode: input.languageCode, contentCode: input.contentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
