import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { IActionResult } from '../../../../../microsoft/asp-net-core/mvc/models';

@Injectable({
  providedIn: 'root',
})
export class PingService {
  apiName = 'Default';
  

  ping = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, IActionResult>({
      method: 'HEAD',
      url: '/api/common/ping',
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
