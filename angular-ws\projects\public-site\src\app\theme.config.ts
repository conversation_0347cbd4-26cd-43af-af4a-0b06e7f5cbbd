import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

export const CustomAuraPreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{orange.50}',
      100: '{orange.100}',
      200: '{orange.200}',
      300: '{orange.300}',
      400: '{orange.400}',
      500: '{orange.500}',
      600: '{orange.600}',
      700: '{orange.700}',
      800: '{orange.800}',
      900: '{orange.900}',
      950: '{orange.950}'
    }
  },
  // primitive: {
  //   orange: {
  //     50: '#fff7ed',
  //     100: '#ffedd5',
  //     200: '#fed7aa',
  //     300: '#fdba74',
  //     400: '#fb923c',
  //     500: '#f97316',
  //     600: '#ea580c',
  //     700: '#c2410c',
  //     800: '#9a3412',
  //     900: '#7c2d12',
  //     950: '#431407'
  //   }
  // }
});
