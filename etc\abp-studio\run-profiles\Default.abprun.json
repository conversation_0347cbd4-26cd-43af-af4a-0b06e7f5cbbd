﻿{
  "applications": {
    "HolyBless.HttpApi.Host": {
      "type": "dotnet-project",
      "launchUrl": "https://localhost:44362",
      "path": "../../../src/HolyBless.HttpApi.Host/HolyBless.HttpApi.Host.csproj",
      "kubernetesService": ".*-httpapihost$",
      "execution": {
        "order": 4
      }
    },
    "HolyBless.Angular": {
      "type": "cli",
      "workingDirectory": "../../../angular",
      "startCommand": "./start.ps1",
      "launchUrl": "http://localhost:4200",
      "kubernetesService": ".*-angular$",
      "execution": {
        "order": 2
      }    
    }
  }
}