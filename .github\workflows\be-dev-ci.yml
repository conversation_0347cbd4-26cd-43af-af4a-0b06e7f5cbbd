name: BE - Dev Build and Test

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'Holybless.sln'
      - 'src/**'
      - 'tests/**'
      - '.github/workflows/be-dev-ci.yml'
      - '.github/workflows/be-dev-cd-public-site.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'Holybless.sln'
      - 'src/**'
      - 'tests/**'
      - '.github/workflows/be-ci.yml'
      - '.github/workflows/be-dev-cd-public-site.yml'

jobs:
  build:

    runs-on: windows-latest

    steps:
    - name: Checkout source code
      uses: actions/checkout@v4

    - name: Setup .NET SDK
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'  # Match the SDK version used by ABP v9

    - name: Restore NuGet packages
      run: dotnet restore

    - name: Build solution
      run: dotnet build Holybless.sln --no-restore --configuration Release

    #- name: Run unit tests
    #  run: dotnet test --no-build --configuration Release --collect:"XPlat Code Coverage"
