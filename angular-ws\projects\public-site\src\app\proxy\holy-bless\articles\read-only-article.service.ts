import type { ArticleDto, TeacherArticleLinkDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { TagDto } from '../tags/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyArticleService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto>({
      method: 'GET',
      url: `/api/app/read-only-article/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleDto>>({
      method: 'GET',
      url: '/api/app/read-only-article',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getTags = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TagDto[]>({
      method: 'GET',
      url: `/api/app/read-only-article/tags/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getTeacherArticleLinks = (studentArticleId: number, skipCount: number, maxResultCount: number, sorting?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<TeacherArticleLinkDto>>({
      method: 'GET',
      url: `/api/app/read-only-article/teacher-article-links/${studentArticleId}`,
      params: { skipCount, maxResultCount, sorting },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
