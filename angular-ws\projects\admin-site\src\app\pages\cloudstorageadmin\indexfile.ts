import { Component, signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Table, TableModule } from 'primeng/table';
import { CheckboxModule, CheckboxChangeEvent } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FileUploadModule } from 'primeng/fileupload';
import { FormsModule } from '@angular/forms';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TagModule } from 'primeng/tag';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { MultiSelectModule } from 'primeng/multiselect';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';

@Component({
  selector: 'app-indexfile',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    CheckboxModule,
    ButtonModule,
    InputTextModule,
    FileUploadModule,
    FormsModule,
    IconFieldModule,
    InputIconModule,
    TagModule,
    DropdownModule,
    CalendarModule,
    DialogModule,
    TreeModule,
    MultiSelectModule,
    ConfirmDialogModule,
    ToastModule,

  ], providers: [ConfirmationService, MessageService],
  template: `
    <div class="card">
      <!-- 工具栏（全选/Import/Remove按钮） -->
      <div class="flex  gap-2 justify-content-between align-items-center mb-4">
      
          <p-checkbox
            [ngModel]="isAllSelected()"
            (onChange)="toggleSelectAll($event)"
            [binary]="true" 
          ></p-checkbox>
  
          <button pButton icon="pi pi-plus" label="Create" class="p-button-primary" (click)="showCreateDialog()"></button>
          <button pButton icon="pi pi-images" label="Add to Album" class="p-button-secondary"></button>
          <button pButton icon="pi pi-file-edit" label="Add Article" class="p-button-secondary"></button>
          <button pButton icon="pi pi-folder" label="Add to Folder" class="p-button-secondary" (click)="showAddToFolderDialog()"></button>
          <button
          pButton
          icon="pi pi-trash"
          class="p-button-danger"
          label="Remove"
          (click)="confirmDelete()"
        ></button>
        <p-confirmDialog
          header="确认删除"
          icon="pi pi-exclamation-triangle"
          acceptButtonStyleClass="p-button-danger"
        ></p-confirmDialog>
        <p-toast></p-toast>
        </div>
        <!-- 创建AddToFolder对话框-->
      <p-dialog 
        header="Add To Folder" 
        [(visible)]="displayAddToFolderDialog" 
        [style]="{width: '450px'}" 
        [modal]="true"
        [draggable]="false"
        [resizable]="false"
        >
        <p-tree
         
          [value]="folders()"
          selectionMode="single"
          [(selection)]="selectedNode"
          (onNodeSelect)="onNodeSelect($event)" >
          <ng-template let-node pTemplate="default">
            <div class="node-container"  >
              <p-checkbox 
                [ngModel]="selectedNode === node"
                (onChange)="toggleNodeSelection(node, $event)"
                [binary]="true"
              ></p-checkbox>
                <span>{{node.label}}</span>
            </div>
          </ng-template>
        </p-tree>
        <ng-template pTemplate="footer">
          <button
            pButton
            label="取消"
            icon="pi pi-times"
            class="p-button-text"
            (click)="displayAddToFolderDialog = false"
          ></button>
          <button
            pButton
            label="确认保存"
            icon="pi pi-check"
            class="p-button-success"
            (click)="confirmSave()"
          ></button>
        </ng-template>
      </p-dialog>
  <!-- 创建文件对话框 -->
      <p-dialog 
        header="Create New File" 
        [(visible)]="displayCreateDialog" 
        [style]="{width: '450px'}" 
        [modal]="true"
        [draggable]="false"
        [resizable]="false"
      >
        <div class="flex flex-col gap-6">
          <div class="col-12">
            <label for="fileName" class="block font-bold mb-3" >File Name</label>
            <input id="fileName" type="text" pInputText [(ngModel)]="newFile.fileName" required autofocus fluid/>
            <small class="text-red-500" *ngIf="!newFile.fileName">Name is required.</small>
          </div>
          <div class="col-12">
            <label for="relativePath" class="block font-bold mb-3" >Relative Path</label>
            <input id="relativePath" type="text" pInputText [(ngModel)]="newFile.relativePath" />
          </div>
          <div class="col-12">
            <label for="contentCategory" class="block font-bold mb-3">Content Category</label>
            <p-dropdown 
              id="contentCategory"
              [(ngModel)]="newFile.contentCategory" 
              [options]="contentCategories"
              placeholder="Select type"
            ></p-dropdown>
          </div>
          <div class="col-12">
            <label for="publishTime" class="block font-bold mb-3">Publish Time</label>
            <p-calendar 
              id="publishTime"
              [(ngModel)]="newFile.publishTime" 
              dateFormat="yy-mm-dd"
              [showTime]="false"
            ></p-calendar>
          </div>
        </div>
        <ng-template pTemplate="footer">
          <button pButton label="Cancel" icon="pi pi-times" (click)="hideCreateDialog()" class="p-button-text"></button>
          <button pButton label="Save" icon="pi pi-check" (click)="saveNewFile()" class="p-button-primary"></button>
        </ng-template>
      </p-dialog>
      <!-- 表格 -->
      <p-table 
        #dt
        [value]="files()" 
        [(selection)]="selectedFiles"
        (selectionChange)="selectedFiles.set($event)"
        dataKey="id"
        [rows]="10" [rowsPerPageOptions]="[10, 25, 50]" [paginator]="true"
        [filters]="filters"
      >
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 3rem"></th>
            <th pSortableColumn="id">ID <p-sortIcon field="id"></p-sortIcon></th>
            <th pSortableColumn="fileName">FileName <p-sortIcon field="fileName"></p-sortIcon>
                <p-columnFilter
                type="text"
                field="fileName"
                display="menu"
                ></p-columnFilter>  
            </th>
            <th pSortableColumn="contentCategory">ContentCategory <p-sortIcon field="contentCategory"></p-sortIcon>
              <p-columnFilter field="contentCategory" matchMode="in" display="menu" [showMatchModes]="false" [showOperator]="false" [showAddButton]="false">
                <ng-template #filter let-value let-filter="filterCallback">
                  <p-multiselect [ngModel]="value" [options]="contentCategories" placeholder="Any" (onChange)="filter($event.value)" [panelStyle]="{ minWidth: '16rem' }" >
                    <ng-template let-option #item>
                      <div class="flex items-center gap-2">
                        <span>{{ option}}</span>
                      </div>
                    </ng-template>
                  </p-multiselect>
                </ng-template>
              </p-columnFilter>
            </th>
            <th pSortableColumn="relativePath">RelativePath <p-sortIcon field="relativePath"></p-sortIcon>
              <p-columnFilter
                  type="text"
                  field="relativePath"
                  display="menu"
              ></p-columnFilter>
            </th>
            <th pSortableColumn="lastModifiedTime">LastModifiedTime <p-sortIcon field="lastModifiedTime"></p-sortIcon></th>
            <th style="width: 8rem"></th>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="body" let-file>
          <tr>
            <td><p-tableCheckbox [value]="file" /></td>
            <td>{{ file.id }}</td>
            <td>
              <span *ngIf="editingRowId !== file.id">{{ file.fileName }}</span>
              <input *ngIf="editingRowId === file.id" 
                    [(ngModel)]="file.fileName" 
                    pInputText>
            </td>
            <td>
              <span *ngIf="editingRowId !== file.id">{{ file.contentCategory }}</span>
              <p-dropdown *ngIf="editingRowId === file.id"
                        [(ngModel)]="file.contentCategory"
                        [options]="contentCategories"
                        placeholder="Select type"></p-dropdown>
            </td>
            <td>
              <span *ngIf="editingRowId !== file.id">{{ file.relativePath }}</span>
              <input *ngIf="editingRowId === file.id" 
                    [(ngModel)]="file.relativePath" 
                    pInputText>
            </td>
            <td>
              <span *ngIf="editingRowId !== file.id">{{ file.lastModifiedTime | date:'medium' }}</span>
              <p-calendar *ngIf="editingRowId === file.id"
                        [(ngModel)]="file.lastModifiedTime"
                        dateFormat="yy-mm-dd"></p-calendar>
            </td>
            <td class="text-right">
              <button *ngIf="editingRowId !== file.id"
              pButton icon="pi pi-pencil" 
              class="p-button-rounded p-button-text mr-2"
              (click)="startEdit(file.id)"></button>
              <button *ngIf="editingRowId === file.id"
                      pButton icon="pi pi-check" 
                      class="p-button-rounded p-button-text p-button-success mr-2"
                      (click)="saveEdit()"></button>
              <button *ngIf="editingRowId === file.id"
                      pButton icon="pi pi-times" 
                      class="p-button-rounded p-button-text p-button-danger"
                      (click)="cancelEdit()"></button>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  `,
  styles: [`
    
  `]
})
export class IndexFile {
  confirmationService = inject(ConfirmationService);
  messageService = inject(MessageService);
  // 初始化过滤器
  filters: any = {
    fileName: [{ value: null, matchMode: 'contains' }],
    contentCategory: [{ value: [], matchMode: 'in' }], // 改为数组和'in'匹配模式
    relativePath: [{ value: null, matchMode: 'contains' }]
  };
  contentCategories = ['pdf', 'doc', 'excel', 'image', 'txt', 'audio', 'folder', 'video'];



  selectedNode: TreeNode | null = null;
  confirmSave() {
    console.log('保存文件夹结构:', this.folders());
    this.displayAddToFolderDialog = false;
    // 这里添加实际保存逻辑
  }

  confirmDelete() {
    if (this.selectedFiles().length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: '未选择文件',
        detail: '请先选择要删除的文件'
      });
      return;
    }

    const fileNames = this.selectedFiles().map(f => f.fileName).join(', ');
    const hasFolders = this.selectedFiles().some(f => f.contentCategory === 'folder');

    this.confirmationService.confirm({
      message: `确定要删除选中的 ${this.selectedFiles().length} 个文件吗？${hasFolders ? '（包含文件夹）' : ''}`,
      header: '删除确认',
      icon: 'pi pi-info-circle',
      acceptLabel: '删除',
      rejectLabel: '取消',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.deleteSelectedFiles();
        this.messageService.add({
          severity: 'success',
          summary: '删除成功',
          detail: `已删除: ${fileNames}`
        });
      }
    });
  }

  deleteSelectedFiles() {
    this.files.update(current =>
      current.filter(file =>
        !this.selectedFiles().some(s => s.id === file.id)
      )
    );
    this.selectedFiles.set([]);
  }
  // 节点选择事件
  onNodeSelect(event: { node: TreeNode }) {
    this.selectedNode = event.node;

  }
  toggleNodeSelection(node: TreeNode, event: CheckboxChangeEvent) {
    const isChecked = event.checked;
    this.selectedNode = isChecked ? node : null;
  }

  folders = signal<TreeNode[]>([
    {
      key: '1',
      label: 'Documents',
      draggable: true,
      droppable: true,
      children: [
        { key: '1-1', label: 'Work', draggable: true, droppable: true, },
        { key: '1-2', label: 'Personal', draggable: true, droppable: true, },
        { key: '1-3', label: 'Notes.txt', draggable: true, droppable: true, },
        { key: '1-4', label: 'Resume.docx', draggable: true, droppable: true, },
      ],
    },
  ]);

  displayAddToFolderDialog = false;
  displayCreateDialog = false;
  newFile = {
    fileName: '',
    relativePath: '',
    contentCategory: '',
    publishTime: new Date()
  };

  editingRowId: number | null = null;

  // 修改：数据模型增加新字段
  files = signal<any[]>([
    {
      id: 1,
      fileName: 'document.pdf',
      contentCategory: 'PDF',
      relativePath: '/documents/project',
      lastModifiedTime: new Date('2023-05-15'),
      publishTime: new Date('2023-05-15')
    },
    {
      id: 2,
      fileName: 'image.png',
      contentCategory: 'Image',
      relativePath: '/images/screenshots',
      lastModifiedTime: new Date('2023-06-20')
    },
    {
      id: 3,
      fileName: 'data.xlsx',
      contentCategory: 'Excel',
      relativePath: '/finance/reports',
      lastModifiedTime: new Date('2023-07-10')
    }
  ]);

  // 选中状态信号（关键修复：初始化空数组）
  selectedFiles = signal<any[]>([]);
  globalSearchText = '';


  showAddToFolderDialog() {
    this.displayAddToFolderDialog = true;
  }
  // 显示创建对话框
  showCreateDialog() {
    this.newFile = {
      fileName: '',
      relativePath: '',
      contentCategory: '',
      publishTime: new Date()
    };
    this.displayCreateDialog = true;
  }

  // 隐藏创建对话框
  hideCreateDialog() {
    this.displayCreateDialog = false;
  }

  // 保存新文件
  saveNewFile() {
    const newId = Math.max(...this.files().map(f => f.id), 0) + 1;

    this.files.update(current => [
      ...current,
      {
        id: newId,
        fileName: this.newFile.fileName,
        contentCategory: this.newFile.contentCategory,
        relativePath: this.newFile.relativePath,
        lastModifiedTime: new Date(),
        publishTime: this.newFile.publishTime
      }
    ]);

    this.displayCreateDialog = false;
  }

  // 在组件类中添加
  startEdit(rowId: number) {
    this.editingRowId = rowId;
  }

  cancelEdit() {
    this.editingRowId = null;
  }

  saveEdit() {
    this.editingRowId = null;
    // 这里可以添加保存到API的逻辑
  }


  // 保持原有计算属性
  isAllSelected = computed(() =>
    this.selectedFiles().length === this.files().length &&
    this.files().length > 0
  );

  // 判断单行是否选中（修复：防御性编程）
  isFileSelected(file: any): boolean {
    return this.selectedFiles().some(f => f.id === file.id);
  }

  // 全选切换（修复：严格类型）
  toggleSelectAll(event: CheckboxChangeEvent) {
    const isChedked = event.checked ?? false;
    this.selectedFiles.set(isChedked ? [...this.files()] : []);
  }

  // 单行切换（修复：空值保护）
  toggleFileSelection(file: any, selected: boolean) {
    this.selectedFiles.update(current => {
      const safeCurrent = Array.isArray(current) ? current : [];
      return selected
        ? [...safeCurrent, file]
        : safeCurrent.filter(f => f.id !== file.id);
    });
  }




  uploadFile(event: any) {
    console.log('File upload triggered', event);
  }

  deleteSelected() {
    this.files.update(current =>
      current.filter(file => !this.selectedFiles().some(s => s.id === file.id))
    );
    this.selectedFiles.set([]);
  }



}
