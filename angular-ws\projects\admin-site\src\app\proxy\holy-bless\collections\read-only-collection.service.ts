import type { CollectionArticleSearchDto, CollectionDto, CollectionFileSearchDto, CollectionSearchDto, CollectionToArticleDto, CollectionToFileDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyCollectionService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/read-only-collection/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticles = (input: CollectionArticleSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionToArticleDto>>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-articles',
      params: { collectionId: input.collectionId, status: input.status, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getCollectionFiles = (input: CollectionFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionToFileDto>>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-files',
      params: { collectionId: input.collectionId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: CollectionSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionDto>>({
      method: 'GET',
      url: '/api/app/read-only-collection',
      params: { status: input.status, channelId: input.channelId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
