import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-withoutcoverlist',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="withoutcoverlist-container p-6">
      <h1 class="text-2xl font-bold mb-4">无封面列表</h1>
      <div class="space-y-4">
        <!-- 这里将显示无封面的列表项 -->
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
          <h3 class="font-semibold text-lg mb-2">示例标题 1</h3>
          <p class="text-gray-600 mb-2">这是一个示例描述文本，展示无封面列表的样式...</p>
          <div class="flex items-center text-sm text-gray-500">
            <span class="mr-4">📅 2024-01-01</span>
            <span>👁️ 123 次查看</span>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
          <h3 class="font-semibold text-lg mb-2">示例标题 2</h3>
          <p class="text-gray-600 mb-2">另一个示例描述文本...</p>
          <div class="flex items-center text-sm text-gray-500">
            <span class="mr-4">📅 2024-01-02</span>
            <span>👁️ 456 次查看</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .withoutcoverlist-container {
      min-height: 400px;
    }
  `]
})
export class WithoutcoverlistComponent {
  constructor() { }
}
