<div class="topbar-start">
    <button pButton #menubutton type="button" class="topbar-menubutton p-trigger duration-300" (click)="onMenuButtonClick()">
        <i class="pi pi-bars"></i>
    </button>
</div>
<div class="layout-topbar-menu-section">
    <div app-sidebar></div>
</div>
<div class="topbar-end">
 <ul class="topbar-menu">
    <li class="topbar-search">
        <p-iconfield>
            <p-inputicon class="pi pi-search" ></p-inputicon>
                        <input pInputText type="text" placeholder="Search" class="w-48 sm:w-full" />
        </p-iconfield>
    </li>
    <li class="topbar-item">
          <button pButton icon="pi pi-globe" [label]="tranlateService.currentLang === 'en' ? '中' : 'En'" class="p-button-text"  (click)="toggleLanguage()"></button>
                </li>
                <li>
                    <a pStyleClass="@next" enterFromClass="!hidden" enterActiveClass="animate-scalein" leaveToClass="!hidden" leaveActiveClass="animate-fadeout" [hideOnOutsideClick]="true" class="cursor-pointer">
                        <img class="rounded-xl w-8 h-8" src="../../../assets/images/avatar-m-1.jpg" alt="Profile" />
                    </a>
                    <ul [class]="'topbar-menu active-topbar-menu !p-6 w-60 z-50 !hidden'">
                        <li role="menuitem" class="!m-0 !mb-4">
                            <a
                                (click)="onTopbarItemClick()"
                                class="cursor-pointer flex items-center hover:text-primary-500 duration-200"
                                pStyleClass="@grandparent"
                                enterFromClass="!hidden"
                                enterActiveClass="animate-scalein"
                                leaveToClass="!hidden"
                                leaveActiveClass="animate-fadeout"
                                [hideOnOutsideClick]="true"
                            >
                                <i class="pi pi-fw pi-lock mr-2"></i>
                                <span>Privacy</span>
                            </a>
                        </li>
                        <li role="menuitem" class="!m-0 !mb-4">
                            <a
                                (click)="onTopbarItemClick()"
                                class="cursor-pointer flex items-center hover:text-primary-500 duration-200"
                                pStyleClass="@grandparent"
                                enterFromClass="!hidden"
                                enterActiveClass="animate-scalein"
                                leaveToClass="!hidden"
                                leaveActiveClass="animate-fadeout"
                                [hideOnOutsideClick]="true"
                            >
                                <i class="pi pi-fw pi-cog mr-2"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li role="menuitem" class="!m-0">
                            <a
                                (click)="onTopbarItemClick()"
                                class="cursor-pointer flex items-center hover:text-primary-500 duration-200"
                                pStyleClass="@grandparent"
                                enterFromClass="!hidden"
                                enterActiveClass="animate-scalein"
                                leaveToClass="!hidden"
                                leaveActiveClass="animate-fadeout"
                                [hideOnOutsideClick]="true"
                            >
                                <i class="pi pi-fw pi-sign-out mr-2"></i>
                                <span>Logout</span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="topbar-item">
            <button pButton type="button" icon="pi pi-cog" class="flex-shrink-0 !rounded-xl !w-8 !h-8" (click)="onConfigButtonClick()"></button>
        </li>
        <li class="topbar-item">
            <button pButton type="button" icon="pi pi-arrow-left" class="flex-shrink-0" text severity="secondary" (click)="onRightMenuButtonClick()"></button>
        </li>
 </ul>
</div>
