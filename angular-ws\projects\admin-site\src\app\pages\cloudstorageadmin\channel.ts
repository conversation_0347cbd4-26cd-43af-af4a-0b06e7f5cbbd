import { Component, signal,  inject, OnInit,Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CloudContentService } from './services/cloudcontent.service'; // 新增
import { FileTreeComponent } from './components/filetree.component'; // 新增
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import {SplitButtonModule} from 'primeng/splitbutton';
import {SplitterModule} from 'primeng/splitter';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { ChannelTableComponent } from './components/channeltable.component';
import { faL } from '@fortawesome/free-solid-svg-icons';


@Component({
  selector: 'app-foldderandfile',
  standalone: true,
  imports: [
    DragDropModule,
    CommonModule,
    TreeModule,
    FileTreeComponent, // 引入树形菜单组件
    SplitterModule,
    SplitButtonModule,
    ChannelTableComponent, // 引入文件列表组件
  ],
  template: `
  <div class="grid grid-cols-12 gap-4">

  <div class="col-span-12 xl:col-span-4">
    <app-file-tree
          [folders]="folders"
          [showAddToChannelButton]="showAddToChannelButton"
          (foldersChange)="onFoldersChange($event)"
          (nodeSelected)="onNodeSelected($event)"
         
        >
        </app-file-tree>
  </div>

  <div class="col-span-12 xl:col-span-8">
       <app-channeltable
       [files]="files()"
           [selectedFolderId]="selectedFolderId()"
          [selectedFolderLabel]="selectedFolderLabel()"
       ></app-channeltable>
  </div>
  
  </div>
    
 

  `,
  styles: [``],
  providers: [CloudContentService],
})
export class Channel implements OnInit {
  showAddToChannelButton = signal(false); // 新增这行
  folders = signal<TreeNode[]>([
    {
      key:'1',
      label: 'Documents',
      draggable:true,
      droppable:true,
      children: [
        { key:'1-1',label: 'Work', draggable:true,droppable:true, },
        { key:'1-2',label: 'Personal', draggable:true,droppable:true, },
        { key:'1-3',label: 'Notes.txt', draggable:true,droppable:true, },
        { key:'1-4',label: 'Resume.docx', draggable:true,droppable:true, },
      ],
    },
  ]);

   // 文件列表（保持不变）
  files = signal([
    { id:'1',title: 'FOLDER', type: 'folder', date: '2024-05-01', selected: false },
    { id:'2',title: 'TEXT.mp4', type: 'audio', date: '2024-05-02', selected: false },
    { id:'3',title: 'TEXT.mp3', type: 'video', date: '2024-05-03', selected: false },
    { id:'4',title: 'TEXT.docx', type: 'doc', date: '2024-05-04', selected: false },
    { id:'5',title: 'TEXT', type: 'txt', date: '2024-05-05', selected: false },
    { id:'6',title: 'TEXT', type: 'txt', date: '2024-05-06', selected: false },
  ]);
  selectedFolderId = signal<string | null>(null);
  selectedFolderLabel = signal<string | null>(null);


  updateFiles(newFiles: any[]) {
  this.files.set(newFiles);
}
  onNodeSelected(node: TreeNode | null) {
      this.selectedFolderId.set(node?.key || null);
      this.selectedFolderLabel.set(node?.label || null);
    }

  onFoldersChange(updatedFolders: TreeNode[]) {
    this.folders.set(updatedFolders);
  }
 

  currentPlayingFile = '';

  cloudContentService = inject(CloudContentService);
  ngOnInit() {}

 

  onPlayFile(file: any) {
    this.currentPlayingFile = file.name;
    console.log('播放文件:', file.name);
  }

  onDownloadFile(file: any) {
    console.log('下载文件:', file.name);
  }
  onSearch(searchParams: any) {
    console.log('搜索参数:', searchParams);
  }
}
