import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-help',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="help-container min-h-screen bg-gray-50">
      <!-- 导航栏 -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a routerLink="/home" class="text-2xl font-bold text-blue-600">HolyBless</a>
              <span class="ml-4 text-gray-500">/ 帮助中心</span>
            </div>
            <div class="flex items-center space-x-6">
              <a routerLink="/help/faq" class="text-gray-700 hover:text-blue-600 font-medium">常见问题</a>
              <a routerLink="/help/contact" class="text-gray-700 hover:text-blue-600 font-medium">联系我们</a>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">帮助中心</h1>
          <p class="text-gray-600 max-w-2xl mx-auto">
            欢迎来到 HolyBless 帮助中心，我们为您提供全面的使用指南和技术支持
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 主要内容区域 -->
          <div class="lg:col-span-2 space-y-8">
            <!-- 快速开始 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-2xl font-semibold text-gray-900 mb-6">快速开始</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="help-card cursor-pointer" (click)="navigateToGuide('ebooks')">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="text-blue-600 text-2xl mb-3">📚</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">电子书使用指南</h3>
                    <p class="text-sm text-gray-600">学习如何上传、阅读和管理您的电子书</p>
                  </div>
                </div>

                <div class="help-card cursor-pointer" (click)="navigateToGuide('storage')">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="text-green-600 text-2xl mb-3">☁️</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">网盘使用指南</h3>
                    <p class="text-sm text-gray-600">了解文件上传、同步和分享功能</p>
                  </div>
                </div>

                <div class="help-card cursor-pointer" (click)="navigateToGuide('podcast')">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="text-purple-600 text-2xl mb-3">🎧</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">播客使用指南</h3>
                    <p class="text-sm text-gray-600">发现如何订阅和收听播客节目</p>
                  </div>
                </div>

                <div class="help-card cursor-pointer" (click)="navigateToGuide('search')">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="text-orange-600 text-2xl mb-3">🔍</div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">搜索使用指南</h3>
                    <p class="text-sm text-gray-600">掌握高效的搜索技巧和过滤功能</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 常见问题预览 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-gray-900">常见问题</h2>
                <a routerLink="/help/faq" class="text-blue-600 hover:text-blue-800 font-medium">查看全部 →</a>
              </div>
              <div class="space-y-4">
                <div class="faq-item cursor-pointer" (click)="toggleFaq(1)">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex justify-between items-center">
                      <h3 class="text-base font-medium text-gray-900">如何上传电子书？</h3>
                      <span class="text-gray-400">+</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">点击电子书页面的"上传"按钮，选择支持的文件格式进行上传...</p>
                  </div>
                </div>

                <div class="faq-item cursor-pointer" (click)="toggleFaq(2)">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex justify-between items-center">
                      <h3 class="text-base font-medium text-gray-900">网盘空间不足怎么办？</h3>
                      <span class="text-gray-400">+</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">您可以删除不需要的文件或升级到更大的存储空间...</p>
                  </div>
                </div>

                <div class="faq-item cursor-pointer" (click)="toggleFaq(3)">
                  <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex justify-between items-center">
                      <h3 class="text-base font-medium text-gray-900">如何订阅播客？</h3>
                      <span class="text-gray-400">+</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">在播客页面找到您感兴趣的节目，点击"订阅"按钮...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="space-y-6">
            <!-- 联系支持 -->
            <div class="bg-blue-50 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-blue-900 mb-4">需要更多帮助？</h3>
              <p class="text-sm text-blue-700 mb-4">
                如果您没有找到需要的答案，我们的支持团队随时为您提供帮助。
              </p>
              <a routerLink="/help/contact" 
                 class="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                联系客服
              </a>
            </div>

            <!-- 用户手册 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">用户手册</h3>
              <div class="space-y-3">
                <a href="#" class="block text-sm text-blue-600 hover:text-blue-800">📖 完整用户手册</a>
                <a href="#" class="block text-sm text-blue-600 hover:text-blue-800">🎥 视频教程</a>
                <a href="#" class="block text-sm text-blue-600 hover:text-blue-800">📝 更新日志</a>
                <a href="#" class="block text-sm text-blue-600 hover:text-blue-800">🔧 API 文档</a>
              </div>
            </div>

            <!-- 社区支持 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">社区支持</h3>
              <div class="space-y-3">
                <a href="#" class="block text-sm text-gray-700 hover:text-blue-600">💬 用户论坛</a>
                <a href="#" class="block text-sm text-gray-700 hover:text-blue-600">📱 微信群</a>
                <a href="#" class="block text-sm text-gray-700 hover:text-blue-600">📧 邮件列表</a>
                <a href="#" class="block text-sm text-gray-700 hover:text-blue-600">🐛 问题反馈</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class HelpComponent {
  navigateToGuide(section: string) {
    console.log('Navigating to guide:', section);
  }

  toggleFaq(id: number) {
    console.log('Toggling FAQ:', id);
  }
}
