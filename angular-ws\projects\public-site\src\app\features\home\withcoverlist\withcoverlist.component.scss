// 主容器样式
.withcoverlist-container {
  min-height: 400px;
  
  // 标题样式
  h1 {
    color: #1f2937;
    font-weight: 700;
  }
}

// 卡片样式
.card-item {
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-radius: 8px;

  &:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
  }

  // 播放时的高亮效果
  &.playing {
    box-shadow: 0 0 0 2px #3b82f6, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-4px) scale(1.02);
    transition: all 0.5s ease;
  }
}

// 筛选工具栏样式
.filters-toolbar {
  
  .date-picker-container {
    width: 150px;

    ::ng-deep {
      .p-calendar {
        width: 100%;
      }
    }
  }
}

// 卡片图片样式
.card-image {
  width: 100%;
  height: 12rem; // h-48 equivalent
  object-fit: cover;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

// 卡片内容样式
.card-content {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.75;
  margin-bottom: 0;
}

// 卡片底部样式
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .view-count {
    font-size: 0.875rem;
    color: #6b7280;
  }
}

// 卡片操作按钮样式
.card-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background: none;
  border: none;
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &.detail-button {
    color: #3b82f6;
    
    &:hover {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
  }

  &.favorite-button {
    color: #10b981;
    
    &:hover {
      background-color: #d1fae5;
      color: #047857;
    }
  }
}

// 分页容器样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  
  .custom-paginator {
    border: none;
    background: transparent;
    
    // 自定义分页器样式
  }
}

