import { Routes } from '@angular/router';

export const appRoutes: Routes = [
  // Landing 页面 - 首页/欢迎页
  {
    path: '',
    redirectTo: '/landing',
    pathMatch: 'full'
  },
  {
    path: 'landing',
    loadComponent: () => import('./pages/landing/landing.component').then(m => m.LandingComponent),
    title: 'HolyBless - 欢迎页'
  },

  // 主站 - 主要内容展示
  {
    path: 'home',
    loadChildren: () => import('./features/home/<USER>').then(m => m.HOME_ROUTES),
    title: 'HolyBless - 主站'
  },

  // 电子书模块
  {
    path: 'ebooks',
    loadChildren: () => import('./features/ebooks/ebooks.routes').then(m => m.EBOOKS_ROUTES),
    title: 'HolyBless - 电子书'
  },

  // 网盘模块
  {
    path: 'storage',
    loadChildren: () => import('./features/storage/storage.routes').then(m => m.STORAGE_ROUTES),
    title: 'HolyBless - 网盘'
  },

  // 播客模块
  {
    path: 'podcast',
    loadChildren: () => import('./features/podcast/podcast.routes').then(m => m.PODCAST_ROUTES),
    title: 'HolyBless - 播客'
  },

  // 搜索模块
  {
    path: 'search',
    loadChildren: () => import('./features/search/search.routes').then(m => m.SEARCH_ROUTES),
    title: 'HolyBless - 搜索'
  },

  // 帮助中心模块
  {
    path: 'help',
    loadChildren: () => import('./features/help/help.routes').then(m => m.HELP_ROUTES),
    title: 'HolyBless - 帮助中心'
  },

  // 404 页面 - 必须放在最后
  {
    path: '**',
    loadComponent: () => import('./pages/not-found/not-found.component').then(m => m.NotFoundComponent),
    title: 'HolyBless - 页面未找到'
  }
];