import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-collectiontree',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="collectiontree-container p-6">
      <h1 class="text-2xl font-bold mb-6">收藏树</h1>
      
      <div class="flex gap-6">
        <!-- 左侧树形结构 -->
        <div class="w-1/3 bg-white rounded-lg shadow-md p-4">
          <h2 class="text-lg font-semibold mb-4">收藏分类</h2>
          <div class="space-y-2">
            <!-- 根节点 -->
            <div class="tree-node">
              <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                <span class="mr-2">📁</span>
                <span class="font-medium">我的收藏</span>
              </div>
              <!-- 子节点 -->
              <div class="ml-6 space-y-1">
                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                  <span class="mr-2">📂</span>
                  <span>技术文章</span>
                  <span class="ml-auto text-sm text-gray-500">(12)</span>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                  <span class="mr-2">📂</span>
                  <span>生活随笔</span>
                  <span class="ml-auto text-sm text-gray-500">(8)</span>
                </div>
                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                  <span class="mr-2">📂</span>
                  <span>学习资料</span>
                  <span class="ml-auto text-sm text-gray-500">(25)</span>
                </div>
                <!-- 嵌套子节点 -->
                <div class="ml-4">
                  <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                    <span class="mr-2">📄</span>
                    <span class="text-sm">前端开发</span>
                    <span class="ml-auto text-sm text-gray-500">(15)</span>
                  </div>
                  <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                    <span class="mr-2">📄</span>
                    <span class="text-sm">后端开发</span>
                    <span class="ml-auto text-sm text-gray-500">(10)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 添加新分类按钮 -->
          <button class="w-full mt-4 p-2 border-2 border-dashed border-gray-300 text-gray-500 rounded hover:border-blue-400 hover:text-blue-500 transition-colors">
            + 添加新分类
          </button>
        </div>
        
        <!-- 右侧内容列表 -->
        <div class="flex-1 bg-white rounded-lg shadow-md p-4">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold">技术文章 (12)</h2>
            <div class="flex items-center space-x-2">
              <select class="px-3 py-1 border rounded text-sm">
                <option>按时间排序</option>
                <option>按标题排序</option>
                <option>按收藏时间排序</option>
              </select>
              <button class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                批量操作
              </button>
            </div>
          </div>
          
          <!-- 收藏项列表 -->
          <div class="space-y-3">
            <div class="flex items-center p-3 border rounded hover:bg-gray-50">
              <input type="checkbox" class="mr-3">
              <div class="flex-1">
                <h3 class="font-medium mb-1">Vue 3 组件开发最佳实践</h3>
                <p class="text-sm text-gray-600 mb-2">详细介绍 Vue 3 中组件开发的最佳实践和注意事项...</p>
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-4">收藏时间: 2024-01-01</span>
                  <span class="mr-4">来源: 技术博客</span>
                  <span>标签: Vue, 前端</span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button class="p-1 text-gray-400 hover:text-blue-500" title="查看">👁️</button>
                <button class="p-1 text-gray-400 hover:text-green-500" title="编辑">✏️</button>
                <button class="p-1 text-gray-400 hover:text-red-500" title="删除">🗑️</button>
              </div>
            </div>
            
            <div class="flex items-center p-3 border rounded hover:bg-gray-50">
              <input type="checkbox" class="mr-3">
              <div class="flex-1">
                <h3 class="font-medium mb-1">React Hooks 深度解析</h3>
                <p class="text-sm text-gray-600 mb-2">深入理解 React Hooks 的工作原理和使用技巧...</p>
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-4">收藏时间: 2024-01-02</span>
                  <span class="mr-4">来源: 官方文档</span>
                  <span>标签: React, Hooks</span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button class="p-1 text-gray-400 hover:text-blue-500" title="查看">👁️</button>
                <button class="p-1 text-gray-400 hover:text-green-500" title="编辑">✏️</button>
                <button class="p-1 text-gray-400 hover:text-red-500" title="删除">🗑️</button>
              </div>
            </div>
            
            <div class="flex items-center p-3 border rounded hover:bg-gray-50">
              <input type="checkbox" class="mr-3">
              <div class="flex-1">
                <h3 class="font-medium mb-1">TypeScript 进阶技巧</h3>
                <p class="text-sm text-gray-600 mb-2">TypeScript 高级类型和泛型的实际应用...</p>
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-4">收藏时间: 2024-01-03</span>
                  <span class="mr-4">来源: 开发者社区</span>
                  <span>标签: TypeScript, 进阶</span>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button class="p-1 text-gray-400 hover:text-blue-500" title="查看">👁️</button>
                <button class="p-1 text-gray-400 hover:text-green-500" title="编辑">✏️</button>
                <button class="p-1 text-gray-400 hover:text-red-500" title="删除">🗑️</button>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="flex items-center justify-center mt-6">
            <div class="flex items-center space-x-2">
              <button class="px-3 py-2 border rounded hover:bg-gray-100 text-sm">上一页</button>
              <button class="px-3 py-2 bg-blue-500 text-white rounded text-sm">1</button>
              <button class="px-3 py-2 border rounded hover:bg-gray-100 text-sm">2</button>
              <button class="px-3 py-2 border rounded hover:bg-gray-100 text-sm">下一页</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .collectiontree-container {
      min-height: 400px;
    }
    .tree-node {
      user-select: none;
    }
  `]
})
export class CollectiontreeComponent {
  constructor() { }
}
