{"name": "1", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@abp/ng.components": "~9.1.0", "@abp/ng.core": "~9.1.0", "@abp/ng.oauth": "~9.1.0", "@abp/ng.setting-management": "~9.1.0", "@abp/ng.theme.shared": "~9.1.0", "@abp/ng.feature-management": "~9.1.0", "@abp/ng.identity": "~9.1.0", "@abp/ng.account": "~9.1.0", "@abp/ng.theme.basic": "~9.1.0", "@angular/animations": "~19.2.0", "@angular/common": "~19.2.0", "@angular/compiler": "~19.2.0", "@angular/core": "~19.2.0", "@angular/forms": "~19.2.0", "@angular/localize": "~19.2.0", "@angular/platform-browser-dynamic": "~19.2.0", "@angular/platform-browser": "~19.2.0", "@angular/router": "~19.2.0", "rxjs": "~7.8.0", "tslib": "^2.0.0", "zone.js": "~0.15.0"}, "devDependencies": {"@abp/ng.schematics": "~9.1.0", "@angular-devkit/build-angular": "~19.2.0", "@angular-eslint/builder": "~19.2.0", "@angular-eslint/eslint-plugin": "~19.2.0", "@angular-eslint/eslint-plugin-template": "~19.2.0", "@angular-eslint/schematics": "~19.2.0", "@angular-eslint/template-parser": "~19.2.0", "@angular/cli": "~19.2.0", "@angular/compiler-cli": "~19.2.0", "@angular/language-service": "~19.2.0", "@types/jasmine": "~3.6.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.0.0", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.0.0", "typescript": "~5.6.0"}}