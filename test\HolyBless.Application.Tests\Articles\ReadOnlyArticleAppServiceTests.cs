using System;
using System.Threading.Tasks;
using HolyBless.Articles;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Results;
using Shouldly;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Xunit;

namespace HolyBless.Articles
{
    public abstract class ReadOnlyArticleAppServiceTests<TStartupModule> : HolyBlessTestBase<TStartupModule>
        where TStartupModule : IAbpModule
    {
        private readonly IReadOnlyArticleAppService _readOnlyArticleAppService;

        protected ReadOnlyArticleAppServiceTests()
        {
            _readOnlyArticleAppService = GetRequiredService<IReadOnlyArticleAppService>();
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Return_Article_With_Files_And_Thumbnail()
        {
            // Arrange - Create test data
            var article = new Article
            {
                Title = "Test Article",
                Description = "Test Description",
                Keywords = "test, keywords",
                Views = 100,
                Likes = 50,
                DeliveryDate = DateTime.Now,
                ArticleContentCategory = ArticleContentCategory.Article,
                Status = PublishStatus.Published,
                Content = "Test content",
                Memo = "Test memo"
            };

            var thumbnailFile = new BucketFile
            {
                FileName = "thumbnail.jpg",
                Title = "Thumbnail Image",
                RelativePathInBucket = "images/thumbnails",
                MediaType = MediaType.Image,
                ContentCategory = ContentCategory.Thumbnail,
                DeliveryDate = DateTime.Now
            };

            var articleFile = new ArticleFile
            {
                Title = "Primary File",
                Description = "Primary article file",
                IsPrimary = true
            };

            var bucketFile = new BucketFile
            {
                FileName = "document.pdf",
                Title = "Article Document",
                RelativePathInBucket = "documents",
                MediaType = MediaType.Document,
                ContentCategory = ContentCategory.Document,
                DeliveryDate = DateTime.Now
            };

            // Insert test data
            await WithUnitOfWorkAsync(async () =>
            {
                var articleRepository = GetRequiredService<IRepository<Article, int>>();
                var bucketFileRepository = GetRequiredService<IRepository<BucketFile, int>>();
                var articleFileRepository = GetRequiredService<IRepository<ArticleFile, int>>();

                thumbnailFile = await bucketFileRepository.InsertAsync(thumbnailFile, autoSave: true);
                bucketFile = await bucketFileRepository.InsertAsync(bucketFile, autoSave: true);
                
                article.ThumbnailFileId = thumbnailFile.Id;
                article = await articleRepository.InsertAsync(article, autoSave: true);
                
                articleFile.ArticleId = article.Id;
                articleFile.FileId = bucketFile.Id;
                await articleFileRepository.InsertAsync(articleFile, autoSave: true);
            });

            // Act
            var result = await _readOnlyArticleAppService.GetArticleAggregatesAsync(article.Id);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(article.Id);
            result.Title.ShouldBe("Test Article");
            result.Description.ShouldBe("Test Description");
            result.Keywords.ShouldBe("test, keywords");
            result.Views.ShouldBe(100);
            result.Likes.ShouldBe(50);
            result.ArticleContentCategory.ShouldBe(ArticleContentCategory.Article);
            result.Status.ShouldBe(PublishStatus.Published);
            result.Content.ShouldBe("Test content");
            result.Memo.ShouldBe("Test memo");

            // Thumbnail assertions
            result.ThumbnailFileId.ShouldBe(thumbnailFile.Id);
            result.ThumbnailFileName.ShouldBe("thumbnail.jpg");
            result.ThumbnailRelativePathInBucket.ShouldBe("images/thumbnails");
            result.ThumbnailMediaType.ShouldBe(MediaType.Image);
            result.ThumbnailUrl.ShouldNotBeNull();

            // Article files assertions
            result.ArticleFiles.ShouldNotBeEmpty();
            result.ArticleFiles.Count.ShouldBe(1);
            
            var fileResult = result.ArticleFiles[0];
            fileResult.ArticleId.ShouldBe(article.Id);
            fileResult.FileId.ShouldBe(bucketFile.Id);
            fileResult.Title.ShouldBe("Primary File");
            fileResult.Description.ShouldBe("Primary article file");
            fileResult.IsPrimary.ShouldBeTrue();
            fileResult.FileName.ShouldBe("document.pdf");
            fileResult.FileTitle.ShouldBe("Article Document");
            fileResult.RelativePathInBucket.ShouldBe("documents");
            fileResult.MediaType.ShouldBe(MediaType.Document);
            fileResult.ContentCategory.ShouldBe(ContentCategory.Document);
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Throw_EntityNotFoundException_For_NonExistent_Article()
        {
            // Act & Assert
            await Should.ThrowAsync<EntityNotFoundException>(async () =>
            {
                await _readOnlyArticleAppService.GetArticleAggregatesAsync(999999);
            });
        }

        [Fact]
        public async Task GetArticleAggregatesAsync_Should_Handle_Article_Without_Thumbnail()
        {
            // Arrange
            var article = new Article
            {
                Title = "Article Without Thumbnail",
                Description = "Test Description",
                DeliveryDate = DateTime.Now,
                ArticleContentCategory = ArticleContentCategory.Article,
                Status = PublishStatus.Published,
                ThumbnailFileId = null // No thumbnail
            };

            await WithUnitOfWorkAsync(async () =>
            {
                var articleRepository = GetRequiredService<IRepository<Article, int>>();
                article = await articleRepository.InsertAsync(article, autoSave: true);
            });

            // Act
            var result = await _readOnlyArticleAppService.GetArticleAggregatesAsync(article.Id);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(article.Id);
            result.Title.ShouldBe("Article Without Thumbnail");
            result.ThumbnailFileId.ShouldBeNull();
            result.ThumbnailFileName.ShouldBeNull();
            result.ThumbnailRelativePathInBucket.ShouldBeNull();
            result.ThumbnailMediaType.ShouldBeNull();
            result.ThumbnailUrl.ShouldBeNull();
            result.ArticleFiles.ShouldBeEmpty();
        }
    }

    // Concrete test class for Application Tests
    public class ApplicationReadOnlyArticleAppServiceTests : ReadOnlyArticleAppServiceTests<HolyBlessApplicationTestModule>
    {
    }
}
