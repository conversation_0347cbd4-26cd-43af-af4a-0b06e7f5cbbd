{"data": [{"listId": "1", "title": "Backlog", "cards": [{"id": "11", "title": "Qualitative resarch planning", "description": "Hey there, we’re just writing to let you know", "startDate": "2022-05-18", "dueDate": "2022-05-18", "completed": false, "progress": 0, "assignees": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}, {"name": "<PERSON>", "image": "amyelsner.png"}], "comments": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png", "text": "How likely are you to recommend our company"}, {"name": "<PERSON>", "image": "amyelsner.png", "text": "Ok thanks!"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 4, "taskList": {"id": "1234", "title": "Tasklist", "tasks": []}}, {"id": "12", "title": "Create new components", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.", "startDate": "2022-05-20", "dueDate": "2022-05-22", "completed": false, "progress": 0, "assignees": [{"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}, {"name": "<PERSON><PERSON><PERSON>", "image": "amyelsner.png"}], "comments": [{"name": "<PERSON><PERSON>", "image": "asiyajavayant.png", "text": "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua"}, {"name": "<PERSON><PERSON><PERSON>", "image": "xuxuefeng.png", "text": "Ut enim ad minim veniam"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 3, "taskList": {"id": "12345", "title": "Tasklist", "tasks": []}}, {"id": "13", "title": "Create new components", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.", "startDate": "2022-05-20", "dueDate": "2022-05-25", "completed": false, "progress": 0, "assignees": [{"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}, {"name": "<PERSON><PERSON><PERSON>", "image": "amyelsner.png"}], "comments": [{"name": "<PERSON><PERSON>", "image": "asiyajavayant.png", "text": "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua"}, {"name": "<PERSON><PERSON><PERSON>", "image": "xuxuefeng.png", "text": "Ut enim ad minim veniam"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 12, "taskList": {"id": "1245", "title": "Tasklist", "tasks": []}}, {"id": "14", "title": "Ut enim ad minim veniam", "startDate": "2022-05-24", "dueDate": "2022-05-26", "completed": false, "progress": 0, "assignees": [{"name": "<PERSON><PERSON><PERSON>", "image": "xuxuefeng.png"}], "comments": [{"name": "Onyama Limba", "image": "onyamalimba.png", "text": "Adipiscing tristique risus nec"}, {"name": "<PERSON>", "image": "amyelsner.png", "text": "Fermentum posuere urna nec"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png", "text": "Sed do eiusmod tempor"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 2, "taskList": {"id": "1345", "title": "Tasklist", "tasks": []}}]}, {"listId": "2", "title": "In Progress", "cards": [{"id": "21", "title": "Qualitative resarch planning", "description": "Hey there, we’re just writing to let you know", "startDate": "2022-05-18", "dueDate": "2022-05-22", "completed": false, "progress": 50, "assignees": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}], "comments": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png", "text": "Ut enim ad minim veniam"}, {"name": "<PERSON>", "image": "amyelsner.png", "text": "<PERSON>uis nostrud exercitation ullamco laboris"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 8, "taskList": {"id": "123", "title": "Tasklist", "tasks": [{"text": "Lorem ipsum dolor sit amet", "completed": false}, {"text": "Consectetur adipiscing elit", "completed": true}, {"text": "Lorem ipsum dolor sit amet", "completed": false}, {"text": "Consectetur adipiscing elit", "completed": true}]}}, {"id": "22", "title": "<PERSON>ulpa qui officia", "description": "Culpa qui officia deserunt mollit anim id est laborum", "startDate": "2022-05-20", "dueDate": "2022-05-26", "completed": false, "progress": 25, "assignees": [{"name": "Onyama Limba", "image": "onyamalimba.png"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png"}, {"name": "<PERSON>", "image": "amyelsner.png"}, {"name": "<PERSON><PERSON><PERSON>", "image": "xuxuefeng.png"}, {"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}], "comments": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png", "text": "Excepteur sint occaecat cupidatat non proident"}, {"name": "<PERSON>", "image": "amyelsner.png", "text": "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua"}], "priority": {"color": "#3b82f6", "title": "Medium"}, "attachments": 12, "taskList": {"id": "2341", "title": "Tasklist", "tasks": [{"text": "Lorem ipsum dolor sit amet", "completed": false}, {"text": "Consectetur adipiscing elit", "completed": true}, {"text": "Lorem ipsum dolor sit amet", "completed": false}, {"text": "Consectetur adipiscing elit", "completed": false}]}}]}]}