import { Component, ElementRef, inject, ViewChild, OnInit, computed } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { StyleClassModule } from 'primeng/styleclass';
import { LayoutService } from '@/layout/services/layout.service';
import { InputText } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { FormsModule } from '@angular/forms';
import { MegaMenuModule } from 'primeng/megamenu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PlayerService } from '@/pages/global-player/services/player.service';
import { MenuService } from '@shared-services/megamenu-topbar/menu.service';

//This component is the top right icon menu items (search, profile, settings, arrow area)
@Component({
  selector: 'app-megamenu-topbar',
  standalone: true,
  imports: [
    RouterModule,
    CommonModule,
    StyleClassModule,
    FormsModule,
    InputText,
    ButtonModule,
    IconField,
    InputIcon,
    MegaMenuModule,
    TranslateModule
  ],
  providers: [TranslateService],
  templateUrl: './megamenu-topbar.component.html',
  styles: [`
   
    `]
})
export class MegaMenuTopbarComponent implements OnInit {
  playerService = inject(PlayerService);
  menuService = inject(MenuService);
  layoutService = inject(LayoutService);
  tranlateService = inject(TranslateService);

  el = inject(ElementRef);

  @ViewChild('menubutton') menuButton!: ElementRef;

  megaMenuItems = this.menuService.menuItems;
  ngOnInit(): void {
    // 新增：设置默认语言
    const browserLang = this.tranlateService.getBrowserLang();
    const defaultLang = browserLang === 'zh' ? 'zh' : 'en';
    this.tranlateService.setDefaultLang(defaultLang);
    this.tranlateService.use(defaultLang);

  }

  onConfigButtonClick() {
    this.layoutService.toggleConfigSidebar();
  }

  // 新增：切换语言的方法
  toggleLanguage() {
    const currentLang = this.tranlateService.currentLang || 'en';
    const newLang = currentLang === 'en' ? 'zh' : 'en';
    this.tranlateService.use(newLang);
    localStorage.setItem('lang', newLang); // 持久化语言选择
  }



}

