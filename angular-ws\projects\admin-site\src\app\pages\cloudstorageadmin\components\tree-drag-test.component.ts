import { Component, signal } from '@angular/core';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-tree-drag-test',
  standalone: true,
  imports: [CommonModule, TreeModule, DragDropModule],
  template: `
    <p-tree 
      [value]="treeData()"
      cdkDropList
      (cdkDropListDropped)="onDrop($event)"
      [style]="{ 'width': '300px', 'user-select': 'none' }"
    >
      <ng-template let-node pTemplate="default">
        <div 
          cdkDrag
          [cdkDragData]="node"
          style="padding: 8px; cursor: grab;"
        >
          {{ node.label }}
        </div>
      </ng-template>
    </p-tree>
    <p>拖拽日志：{{ dragLog() }}</p>
  `,
  styles: [`
    .cdk-drag-preview {
      background: white;
      box-shadow: 0 3px 5px rgba(0,0,0,0.2);
      padding: 8px;
      border-radius: 4px;
    }
  `]
})
export class TreeDragTestComponent {
  // 测试数据
  treeData = signal<TreeNode[]>([
    {
      label: '节点1',
      children: [
        { label: '节点1.1' },
        { label: '节点1.2' }
      ]
    },
    {
      label: '节点2',
      children: [
        { label: '节点2.1' },
        { label: '节点2.2' }
      ]
    }
  ]);

  dragLog = signal<string>('未开始拖拽');

  onDrop(event: CdkDragDrop<TreeNode[]>) {
    this.dragLog.set(`从 ${event.previousIndex} 移动到 ${event.currentIndex}`);
    console.log('拖拽事件:', event);
    
    // 实际移动数据的逻辑（此处仅演示）
    const movedItem = {...event.item.data};
    this.treeData.update(data => {
      const newData = [...data];
      newData.splice(event.previousIndex, 1);
      newData.splice(event.currentIndex, 0, movedItem);
      return newData;
    });
  }
}
