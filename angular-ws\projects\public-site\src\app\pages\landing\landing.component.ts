import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ImageModule } from 'primeng/image';
import { CardModule } from 'primeng/card';
import { GalleriaModule } from 'primeng/galleria';
import { DataViewModule } from 'primeng/dataview';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [CommonModule, RouterModule, ImageModule, CardModule, GalleriaModule, DataViewModule, ButtonModule, DividerModule, TagModule],
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss']
})
export class LandingComponent implements OnInit {
  galleriaResponsiveOptions = [
    {
      breakpoint: '1024px',
      numVisible: 1
    },
    {
      breakpoint: '768px',
      numVisible: 1
    },
    {
      breakpoint: '560px',
      numVisible: 1
    }
  ];

  miracleItems = [
    {
      title: '数字化转型',
      description: '探索企业数字化转型的最佳实践和成功案例',
      creationTime: '2024-06-01'
    },
    {
      title: '人工智能革命',
      description: 'AI技术如何改变我们的工作和生活方式',
      creationTime: '2024-06-15'
    },
    {
      title: '可持续发展',
      description: '绿色科技与环保创新的未来趋势',
      creationTime: '2024-06-20'
    },
    {
      title: '远程协作',
      description: '分布式团队的高效协作方法和工具',
      creationTime: '2024-06-25'
    },
    {
      title: '区块链应用',
      description: '区块链技术在各行业的实际应用场景',
      creationTime: '2024-06-28'
    },
    {
      title: '物联网时代',
      description: '万物互联如何重塑我们的智能生活',
      creationTime: '2024-06-30'
    }
  ];

  lectureItems = [
    {
      title: '深度学习基础',
      cover: 'assets/images/20240517神之语.jpg'
    },
    {
      title: '云计算架构设计',
      cover: 'assets/images/早安心语1.jpg'
    },
    {
      title: '前端开发实战',
      cover: 'assets/images/灵音.jpg'
    },
    {
      title: '数据科学入门',
      cover: 'assets/images/default-cover.jpg'
    }
  ];

  tianmenkaiItems = [
    {
      title: '冥想指南',
      cover: 'assets/images/0309证量.jpg'
    },
    {
      title: '瑜伽练习',
      cover: 'assets/images/20240517神之语.jpg'
    },
    {
      title: '正念生活',
      cover: 'assets/images/早安心语1.jpg'
    },
    {
      title: '心理健康',
      cover: 'assets/images/灵音.jpg'
    },
    {
      title: '呼吸练习',
      cover: 'assets/images/0309证量.jpg'
    },
    {
      title: '内在平静',
      cover: 'assets/images/早安心语1.jpg'
    }
  ];

  topItems = [
    {
      title: '人工智能导论',
      score: '9.8'
    },
    {
      title: '现代Web开发',
      score: '9.6'
    },
    {
      title: '数据结构与算法',
      score: '9.5'
    },
    {
      title: '机器学习实战',
      score: '9.4'
    },
    {
      title: '云原生架构',
      score: '9.3'
    }
  ];

  noticeItems = [
    {
      title: '系统维护通知',
      type: '维护',
      date: '2024-07-02',
      severity: 'warning'
    },
    {
      title: '新功能发布',
      type: '更新',
      date: '2024-07-01',
      severity: 'success'
    },
    {
      title: '重要安全更新',
      type: '安全',
      date: '2024-06-30',
      severity: 'danger'
    },
    {
      title: '用户协议更新',
      type: '协议',
      date: '2024-06-28',
      severity: 'info'
    },
    {
      title: '版本升级通知',
      type: '升级',
      date: '2024-06-25',
      severity: 'success'
    },
    {
      title: '性能优化完成',
      type: '优化',
      date: '2024-06-20',
      severity: 'info'
    },
    {
      title: '新增多语言支持',
      type: '功能',
      date: '2024-06-18',
      severity: 'success'
    },
    {
      title: '数据备份策略调整',
      type: '策略',
      date: '2024-06-15',
      severity: 'warning'
    },
    {
      title: '界面优化更新',
      type: 'UI',
      date: '2024-06-12',
      severity: 'info'
    }
  ];

  ngOnInit() {
    // 组件初始化逻辑
  }
}
