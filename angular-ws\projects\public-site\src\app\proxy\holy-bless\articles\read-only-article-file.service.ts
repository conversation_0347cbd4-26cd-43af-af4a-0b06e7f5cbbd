import type { ArticleFileDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyArticleFileService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto>({
      method: 'GET',
      url: `/api/app/read-only-article-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getByArticleId = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto[]>({
      method: 'GET',
      url: `/api/app/read-only-article-file/by-article-id/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleFileDto>>({
      method: 'GET',
      url: '/api/app/read-only-article-file',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getPrimaryFiles = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto[]>({
      method: 'GET',
      url: `/api/app/read-only-article-file/primary-files/${articleId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
