import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-ebooks',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="ebooks-container min-h-screen bg-gray-50">
      <!-- 导航栏 -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a routerLink="/home" class="text-2xl font-bold text-blue-600">HolyBless</a>
              <span class="ml-4 text-gray-500">/ 电子书</span>
            </div>
            <div class="flex items-center space-x-6">
              <a routerLink="/ebooks/library" class="text-gray-700 hover:text-blue-600 font-medium">我的书库</a>
              <a routerLink="/storage" class="text-gray-700 hover:text-blue-600 font-medium">网盘</a>
              <a routerLink="/search" class="text-gray-700 hover:text-blue-600 font-medium">搜索</a>
            </div>
          </div>
        </div>
      </nav>

      <!-- 主要内容 -->
      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">电子书中心</h1>
          <p class="text-gray-600">发现、阅读和管理您的电子书收藏</p>
        </div>

        <!-- 功能区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div class="lg:col-span-2">
            <!-- 推荐阅读 -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">推荐阅读</h2>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="book-card cursor-pointer" (click)="openBook('1')">
                  <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg aspect-[3/4] mb-2 flex items-center justify-center">
                    <span class="text-white text-xs font-medium">封面</span>
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 truncate">示例电子书 1</h3>
                  <p class="text-xs text-gray-600">作者名</p>
                </div>
                <div class="book-card cursor-pointer" (click)="openBook('2')">
                  <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-lg aspect-[3/4] mb-2 flex items-center justify-center">
                    <span class="text-white text-xs font-medium">封面</span>
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 truncate">示例电子书 2</h3>
                  <p class="text-xs text-gray-600">作者名</p>
                </div>
                <div class="book-card cursor-pointer" (click)="openBook('3')">
                  <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg aspect-[3/4] mb-2 flex items-center justify-center">
                    <span class="text-white text-xs font-medium">封面</span>
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 truncate">示例电子书 3</h3>
                  <p class="text-xs text-gray-600">作者名</p>
                </div>
                <div class="book-card cursor-pointer" (click)="openBook('4')">
                  <div class="bg-gradient-to-br from-red-400 to-red-600 rounded-lg aspect-[3/4] mb-2 flex items-center justify-center">
                    <span class="text-white text-xs font-medium">封面</span>
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 truncate">示例电子书 4</h3>
                  <p class="text-xs text-gray-600">作者名</p>
                </div>
              </div>
            </div>

            <!-- 最近阅读 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">最近阅读</h2>
              <div class="space-y-3">
                <div class="flex items-center p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100" (click)="openBook('1')">
                  <div class="bg-blue-500 rounded w-12 h-16 mr-4 flex items-center justify-center">
                    <span class="text-white text-xs">📖</span>
                  </div>
                  <div class="flex-1">
                    <h3 class="text-sm font-medium text-gray-900">示例电子书 1</h3>
                    <p class="text-xs text-gray-600">阅读进度：45%</p>
                    <p class="text-xs text-gray-500">最后阅读：2 小时前</p>
                  </div>
                </div>
                <div class="flex items-center p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100" (click)="openBook('2')">
                  <div class="bg-green-500 rounded w-12 h-16 mr-4 flex items-center justify-center">
                    <span class="text-white text-xs">📖</span>
                  </div>
                  <div class="flex-1">
                    <h3 class="text-sm font-medium text-gray-900">示例电子书 2</h3>
                    <p class="text-xs text-gray-600">阅读进度：78%</p>
                    <p class="text-xs text-gray-500">最后阅读：1 天前</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="space-y-6">
            <!-- 快速操作 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
              <div class="space-y-3">
                <button class="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors" 
                        (click)="navigateToLibrary()">
                  <div class="flex items-center">
                    <span class="text-blue-600 mr-3">📚</span>
                    <span class="text-sm font-medium text-blue-900">浏览书库</span>
                  </div>
                </button>
                <button class="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-green-600 mr-3">⬆️</span>
                    <span class="text-sm font-medium text-green-900">上传电子书</span>
                  </div>
                </button>
                <button class="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-purple-600 mr-3">⭐</span>
                    <span class="text-sm font-medium text-purple-900">我的收藏</span>
                  </div>
                </button>
              </div>
            </div>

            <!-- 阅读统计 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">阅读统计</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">本月阅读</span>
                  <span class="text-sm font-medium text-gray-900">12 小时</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">已读书籍</span>
                  <span class="text-sm font-medium text-gray-900">8 本</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-gray-600">总计藏书</span>
                  <span class="text-sm font-medium text-gray-900">45 本</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .book-card:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease-in-out;
    }
  `]
})
export class EbooksComponent {
  openBook(bookId: string) {
    console.log('Opening book:', bookId);
    // 这里可以导航到阅读器页面
  }

  navigateToLibrary() {
    console.log('Navigating to library');
    // 导航到书库页面
  }
}
