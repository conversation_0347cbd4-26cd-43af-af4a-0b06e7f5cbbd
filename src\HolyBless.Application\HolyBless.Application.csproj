<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\common.props" />
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>HolyBless</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="StorageProviders\S3StorageManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HolyBless.Domain\HolyBless.Domain.csproj" />
    <ProjectReference Include="..\HolyBless.Application.Contracts\HolyBless.Application.Contracts.csproj" />
    <ProjectReference Include="..\HolyBless.FakeData\HolyBless.FakeData.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="3.7.416.12" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.1.1" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Articles\" />
  </ItemGroup>
</Project>