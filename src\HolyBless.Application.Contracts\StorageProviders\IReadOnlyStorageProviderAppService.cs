﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace HolyBless.StorageProviders
{
    public interface IReadOnlyStorageProviderAppService: IApplicationService
    {
        Task<string> GetFileAccessUrlSync(string fileName, string relativePath, string preferCountry);
        Task<StorageProviderDto> GetProvidersByCountry(string preferCountry);
    }
}
