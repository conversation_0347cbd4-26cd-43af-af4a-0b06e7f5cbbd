name: BE - <PERSON> Deploy to Azure App Service

on:
  workflow_run:
    workflows: ['BE - Dev Build and Test']  # Name of your BE Dev CI workflow
    types:
      - completed
    branches: [ main ]
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  BUILD_CONFIGURATION: 'Release'
  DOTNET_ROOT: ${{ github.workspace }}/dotnet

jobs:
  build:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    environment: Dev  # This is the environment name in GitHub Repository Settings -> Environments
    env:
      ASPNETCORE_ENVIRONMENT: Dev
      DOTNET_ENVIRONMENT: Dev
      ConnectionStrings__Default: ${{ secrets.DB_CONNECTION_STRING }}
      App__SelfUrl: ${{ vars.APP_SELFURL }}
      App__AngularUrl: ${{ vars.APP_ANGULARURL }}
      App__CorsOrigins: ${{ vars.APP_CORSORIGINS }}
      App__RedirectAllowedUrls: ${{ vars.APP_REDIRECTALLOWEDURLS }}
      AuthServer__Authority: ${{ vars.AUTHSERVER_AUTHORITY }}
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET Core
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{env.DOTNET_VERSION}}

      - name: Restore dependencies
        run: dotnet restore HolyBless.sln

      - name: Build
        run: dotnet build HolyBless.sln --no-restore --configuration ${{env.BUILD_CONFIGURATION}}

      - name: dotnet publish
        run: |
          dotnet publish src/HolyBless.HttpApi.Host/HolyBless.HttpApi.Host.csproj -c ${{env.BUILD_CONFIGURATION}} -o ${{env.DOTNET_ROOT}}/apihost
          dotnet publish src/HolyBless.DbMigrator/HolyBless.DbMigrator.csproj -c ${{env.BUILD_CONFIGURATION}} -o ${{env.DOTNET_ROOT}}/dbmigrator

      - name: Inject Dev secrets and vars into appsettings.json
        run: |
          $file = "${{env.DOTNET_ROOT}}/apihost/appsettings.json"
          $json = Get-Content $file | ConvertFrom-Json
          $json.ConnectionStrings.Default = "${{ secrets.DB_CONNECTION_STRING }}"
          $json.App.SelfUrl = "${{ vars.APP_SELFURL }}"
          $json.App.AngularUrl = "${{ vars.APP_ANGULARURL }}"
          $json.App.CorsOrigins = "${{ vars.APP_CORSORIGINS }}"
          $json.App.RedirectAllowedUrls = "${{ vars.APP_REDIRECTALLOWEDURLS }}"
          $json.AuthServer.Authority = "${{ vars.AUTHSERVER_AUTHORITY }}"
          $json | ConvertTo-Json -Depth 10 | Set-Content $file
        shell: pwsh
      
      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: .api-host
          path: ${{env.DOTNET_ROOT}}/apihost

      - name: Upload artifact for db migrator job
        uses: actions/upload-artifact@v4
        with:
          name: .db-migrator
          path: ${{env.DOTNET_ROOT}}/dbmigrator

      - name: Create Db Migration
        run: |
          cd src/HolyBless.DbMigrator
          dotnet run --configuration ${{env.BUILD_CONFIGURATION}} -- --environment dev

  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Dev'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write #This is required for requesting the JWT
      contents: read #This is required for actions/checkout

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: .api-host
      
      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'api-dev-public-holybless'
          slot-name: 'Production'
          package: .
