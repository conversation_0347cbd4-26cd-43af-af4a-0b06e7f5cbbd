using HolyBless.Enums;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Lookups.Dtos
{
    public class CountryDto : EntityDto<int>
    {
        public string Name { get; set; } = default!;
        public string Code { get; set; } = default!;
        public string Code3 { get; set; } = default!;
        public string DefaultLangCode { get; set; } = LangCode.English;
        public string DefaultSpokenLangCode { get; set; } = SpokenLangCode.English;
    }
}