/* Styles for the home component */
.card-bg-image {
  position: absolute;
  width: 630px;
  height: auto;
  bottom: 0;
  right: 0;
  z-index: 1;
}

.starting-content {
  position: relative;
  z-index: 2;
}

.bg-check-icon {
  width: 20px;
  height: 20px;
  color: #f72585 !important;
  background-color: rgba(247, 37, 133, 0.1) !important;
}

.abp-logo {
  background-repeat: no-repeat;
}

.abp-support {
  width: 178px;
  height: 30px;
  background-image: url('/assets/images/getting-started/abp-support.svg');
}

.abp-community {
  width: 215px;
  height: 30px;
  background-image: url('/assets/images/getting-started/abp-community.svg');
}

.abp-blog {
  width: 142px;
  height: 30px;
  background-image: url('/assets/images/getting-started/abp-blog.svg');
}

.logo-x {
  width: 20px;
  height: 20px;
  background-image: url('/assets/images/getting-started/x.svg');
}

@media (min-width: 1199px) {
  .h-lg-100 {
    height: 100% !important;
  }
}

@media (min-width: 768px) {
  .h-md-100 {
    height: 100% !important;
  }
}
