import { Component, signal, inject, OnInit, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CloudContentService } from './services/cloudcontent.service'; // 新增
import { FileTreeComponent } from './components/filetree.component'; // 新增
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { SplitButtonModule } from 'primeng/splitbutton';
import { SplitterModule } from 'primeng/splitter';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { FolderAndFileTableComponent } from './components/folderandfiletable.component'; // 新增导入

@Component({
  selector: 'app-foldderandfile',
  standalone: true,
  imports: [
    DragDropModule,
    CommonModule,
    TreeModule,
    FileTreeComponent, // 引入树形菜单组件
    SplitterModule,
    SplitButtonModule,
    FolderAndFileTableComponent, // 引入文件列表组件
  ],
  template: `
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 xl:col-span-4">
        <app-file-tree
          [folders]="folders"
          (foldersChange)="onFoldersChange($event)"
          (nodeSelected)="onNodeSelected($event)"
          [selectedFolderId]="selectedFolderId()"
          [selectedFolderLabel]="selectedFolderLabel()"
        >
        </app-file-tree>
      </div>

      <div class="col-span-12 xl:col-span-8">
        <app-folderandfiletable
          [files]="files()"
          [selectedFolderId]="selectedFolderId()"
          [selectedFolderLabel]="selectedFolderLabel()"
        ></app-folderandfiletable>
      </div>
    </div>
  `,
  styles: [``],
  providers: [CloudContentService],
})
export class FolderAndFile implements OnInit {
  folders = signal<TreeNode[]>([
    {
      key: '1',
      label: 'Documents',
      draggable: true,
      droppable: true,
      children: [
        { key: '1-1', label: 'Work', draggable: true, droppable: true, },
        { key: '1-2', label: 'Personal', draggable: true, droppable: true, },
        { key: '1-3', label: 'Notes.txt', draggable: true, droppable: true, },
        { key: '1-4', label: 'Resume.docx', draggable: true, droppable: true, },
      ],
    },
  ]);

  // 文件列表（保持不变）
  files = signal([
    {
      id: '1',
      fileName: 'FOLDER',
      contentCategory: 'folder',
      date: '2024-05-01',
      selected: false,
    },
    {
      id: '2',
      fileName: 'TEXT.mp4',
      contentCategory: 'audio',
      date: '2024-05-02',
      selected: false,
    },
    {
      id: '3',
      fileName: 'TEXT.mp3',
      contentCategory: 'video',
      date: '2024-05-03',
      selected: false,
    },
    {
      id: '4',
      fileName: 'TEXT.docx',
      contentCategory: 'doc',
      date: '2024-05-04',
      selected: false,
    },
    {
      id: '5',
      fileName: 'TEXT',
      contentCategory: 'txt',
      date: '2024-05-05',
      selected: false,
    },
    {
      id: '6',
      fileName: 'TEXT',
      contentCategory: 'txt',
      date: '2024-05-06',
      selected: false,
    },
  ]);
  selectedFolderId = signal<string | null>(null);
  selectedFolderLabel = signal<string | null>(null);

  onNodeSelected(node: TreeNode | null) {
    this.selectedFolderId.set(node?.key || null);
    this.selectedFolderLabel.set(node?.label || null);
  }

  onFoldersChange(updatedFolders: TreeNode[]) {
    this.folders.set(updatedFolders);
  }

  currentPlayingFile = '';

  cloudContentService = inject(CloudContentService);
  ngOnInit() { }

  onPlayFile(file: any) {
    this.currentPlayingFile = file.name;
    console.log('播放文件:', file.name);
  }

  onDownloadFile(file: any) {
    console.log('下载文件:', file.name);
  }
  onSearch(searchParams: any) {
    console.log('搜索参数:', searchParams);
  }
}
