{"data": [{"id": 1, "name": "Create a New Landing UI", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-13", "endDate": "2022-05-15", "members": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}, {"name": "<PERSON>", "image": "amyelsner.png"}, {"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}], "completed": false, "status": "Approved", "comments": 3, "attachments": 2}, {"id": 2, "name": "Create Dashboard", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-16", "endDate": "2022-05-20", "members": [{"name": "Onyama Limba", "image": "onyamalimba.png"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png"}], "completed": false, "status": "Waiting", "comments": 2, "attachments": 4}, {"id": 3, "name": "Brand logo design", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-17", "endDate": "2022-05-18", "members": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}, {"name": "<PERSON>", "image": "amyelsner.png"}, {"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}], "completed": null, "status": "Approved", "comments": 4, "attachments": 1}, {"id": 4, "name": "Create Dashboard", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-20", "endDate": "2022-05-22", "members": [{"name": "Onyama Limba", "image": "onyamalimba.png"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png"}], "completed": null, "status": "Approved", "comments": 1, "attachments": 3}, {"id": 5, "name": "Rebranding For Peak", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-24", "endDate": "2022-05-26", "members": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}, {"name": "<PERSON>", "image": "amyelsner.png"}], "completed": false, "status": "Waiting", "comments": 5, "attachments": 2}, {"id": 6, "name": "Create Mobile UI", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-13", "endDate": "2022-05-15", "members": [{"name": "Onyama Limba", "image": "onyamalimba.png"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png"}], "completed": null, "status": "Approved", "comments": 1, "attachments": 2}, {"id": 7, "name": "Create a New Marketing Project", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-13", "endDate": "2022-05-15", "members": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}, {"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}, {"name": "<PERSON><PERSON><PERSON>", "image": "xuxuefeng.png"}], "completed": true, "status": "Complete", "comments": 7, "attachments": 4}, {"id": 8, "name": "Analyze New Sprint", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-10", "endDate": "2022-05-12", "members": [{"name": "<PERSON><PERSON>", "image": "ionibowcher.png"}], "completed": true, "status": "Complete", "comments": 3, "attachments": 2}, {"id": 9, "name": "Create New Icon Set", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Enim eu turpis egestas pretium aenean pharetra magna.", "startDate": "2022-05-13", "endDate": "2022-05-15", "members": [{"name": "Onyama Limba", "image": "onyamalimba.png"}, {"name": "<PERSON>", "image": "ivanmagalhaes.png"}, {"name": "<PERSON><PERSON>", "image": "asiyajavayant.png"}], "completed": true, "status": "Complete", "comments": 1, "attachments": 3}]}