:root {

    //为圣光临在修改
    --primary-color:rgb(66, 90, 112);
    --second-color:rgba(67, 90, 111, 0.14);
    --third-color:white;


   // 覆盖所有PrimeNG组件的圆角
   .p-component,
   .p-card,
   .p-datatable,
   .p-inputtext,
   .p-dropdown,
   .p-tag,
   .card,
   .p-button {
     border-radius: 3px;
   }
  .p-tree,
  .p-card,
  .card,
  .content-area,
  .p-paginator,
  .p-select,
  .p-select-list-container,
  .p-select-list {
    background: unset;
    box-shadow: unset;
  }



 


      .p-tree {
        padding:unset;
        background: transparent;
      }
      // 特殊处理表格单元格
  .p-datatable .p-datatable-thead>tr>th,
  .p-datatable .p-datatable-tbody>tr>td {
    border-radius: 0;
  }
  
  .p-card-body {
    padding: 0%;
  }



}