<p-megamenu  [model]="megaMenuItems()"
*ngIf="megaMenuItems().length > 0 && megaMenuItems() as items"  orientation="horizontal" >
  <ng-template #start>
    <img src="assets/images/logo.svg" class="w-12 h-12 mb-3" alt="Logo" />
  </ng-template>
  <ng-template #item let-item>
    <a *ngIf="item.root"  [routerLink]="item.routerLink"  pRipple class="flex items-center cursor-pointer px-4 py-2 overflow-hidden relative font-semibold text-lg uppercase" style="border-radius: 2rem">
        <i [ngClass]="item.icon"></i>
        <span class="ml-2">{{ item.label | translate}}</span>
    </a>
    <ng-container *ngIf="!item.root && !item.image" >
    <a  *ngIf="item.routerLink" [routerLink]="item.id? [item.routerLink, item.id] : item.routerLink"  class="flex items-center p-0 cursor-pointer mb-2 gap-2">
        <span class="inline-flex items-center justify-center rounded-full bg-second text-second-contrast w-8 h-8">
          <img src="assets/images/主站leftside.svg" class="w-8 h-8 text-lg" alt="Logo" />
        </span>
        <span class="inline-flex flex-col gap-1">
            <span class="font-medium text-lg text-surface-900 dark:text-surface-0">{{ item.label | translate}} </span>
            <span class="whitespace-nowrap">{{ item.subtext | translate }}</span>
        </span>
    </a>
    <a  *ngIf="!item.routerLink" [attr.href]="item.url" [attr.target]="item.target || '_blank'" class="flex items-center p-4 cursor-pointer mb-2 gap-2">
        <span class="inline-flex items-center justify-center rounded-full bg-second text-second-contrast w-8 h-8">
          <img src="assets/images/主站leftside.svg" class="w-8 h-8 text-lg" alt="Logo" />
        </span>
        <span class="inline-flex flex-col gap-1">
            <span class="font-medium text-lg text-surface-900 dark:text-surface-0">{{ item.label | translate}} </span>
            <span class="whitespace-nowrap">{{ item.subtext | translate }}</span>
        </span>
    </a>
    </ng-container>
   
    <div *ngIf="item.image" class="flex flex-col items-start gap-4">
        <img [src]="item.image" alt="megamenu-demo" class="w-full" />
        <span>{{ item.subtext | translate }}</span>
        <p-button [label]="item.label" [outlined]="true"></p-button>
    </div>
</ng-template>
  <ng-template #end>
    <p-iconfield>
      <p-inputicon class="pi pi-search"></p-inputicon>
      <input pInputText type="text" placeholder="Search" class="w-full" />
    </p-iconfield>
    <button
        pButton
        icon="pi pi-globe"
        class="p-ripple p-button p-component p-button-secondary p-button-text mr-2"
        [label]="tranlateService.currentLang === 'en' ? '中' : 'En'"
        (click)="toggleLanguage()"
      ></button>
       <!-- 音乐播放器按钮 -->
    <button
    pButton
      icon="fas fa-music"
      class="p-ripple p-button p-component p-button-secondary p-button-text mr-2"
      (click)="playerService.togglePlayer()">
    </button>
    <button
        pButton
        type="button"
        icon="pi pi-cog"
        class="p-ripple p-button p-component p-button-secondary p-button-text mr-2"
        (click)="onConfigButtonClick()"
      ></button>
  </ng-template>
</p-megamenu>