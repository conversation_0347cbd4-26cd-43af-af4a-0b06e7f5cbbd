// pages/service/cloudcontent.service.ts
import { Injectable } from '@angular/core';
import { TreeNode } from 'primeng/api';

@Injectable({ providedIn: 'root' })
export class CloudContentService {
    getMenuItems(): TreeNode[] {
        return [
            {
                key: '0',
                label: '源声 (Root Folder)',
                data: '源声 (Root Folder)',

            },
            {
                key: '1',
                label: '影音创作',
                data: '影音创作',

            },
            {
                key: '2',
                label: '灌卡专用',
                children: [
                    {
                        key: '2-0',
                        label: '文档 (Child Folder)',
                    },
                    {
                        key: '2-1',
                        label: '音频',
                    },
                    {
                        key: '2-2',
                        label: '视频',
                    },
                ],
            },
        ];
    }
}
