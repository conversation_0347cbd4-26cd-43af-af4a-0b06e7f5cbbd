using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Buckets.Dtos
{
    public class CreateUpdateBucketDto
    {
        [Required]
        public int StorageProviderId { get; set; }

        [Required]
        public string BucketName { get; set; } = default!;

        [Required]
        public string LanguageCode { get; set; } = default!;

        public string? SpokenLangCode { get; set; }

        [Required]
        public string SubDomain { get; set; } = default!;

        public ContentCategory ContentType { get; set; }

    }
}