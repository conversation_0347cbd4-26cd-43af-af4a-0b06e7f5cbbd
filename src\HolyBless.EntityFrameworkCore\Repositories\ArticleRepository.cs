using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.EntityFrameworkCore;
using HolyBless.Results;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using HolyBless.Domain.Interfaces;

namespace HolyBless.Repositories
{
    public class ArticleRepository : EfCoreRepository<HolyBlessDbContext, Article, int>, IArticleRepository
    {
        public ArticleRepository(IDbContextProvider<HolyBlessDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<ArticleAggregateResult?> GetArticleAggregateAsync(int articleId)
        {
            var dbContext = await GetDbContextAsync();

            var article = await dbContext.Articles
                .Include(a => a.ArticleFiles)
                    .ThenInclude(af => af.BucketFile)
                .Include(a => a.ThumbnailBucketFile)
                .FirstOrDefaultAsync(a => a.Id == articleId);

            if (article == null)
            {
                return null;
            }

            var result = new ArticleAggregateResult
            {
                Id = article.Id,
                Title = article.Title,
                Description = article.Description,
                Keywords = article.Keywords,
                Views = article.Views,
                Likes = article.Likes,
                DeliveryDate = article.DeliveryDate,
                ArticleContentCategory = article.ArticleContentCategory,
                Status = article.Status,
                Content = article.Content,
                Memo = article.Memo,
                CreationTime = article.CreationTime,
                LastModificationTime = article.LastModificationTime,
                ThumbnailFileId = article.ThumbnailFileId,
                ThumbnailFileName = article.ThumbnailBucketFile?.FileName,
                ThumbnailRelativePathInBucket = article.ThumbnailBucketFile?.RelativePathInBucket,
                ArticleFiles = article.ArticleFiles?.Select(af => new ArticleFileAggregateResult
                {
                    Id = af.Id,
                    FileId = af.FileId,
                    Title = af.Title,
                    Description = af.Description,
                    IsPrimary = af.IsPrimary,
                    FileName = af.BucketFile.FileName,
                    FileTitle = af.BucketFile.Title,
                    RelativePathInBucket = af.BucketFile.RelativePathInBucket,
                    MediaType = af.BucketFile.MediaType,
                    ContentCategory = af.BucketFile.ContentCategory,
                    FileDeliveryDate = af.BucketFile.DeliveryDate,
                    FileViews = af.BucketFile.Views,
                    YoutubeId = af.BucketFile.YoutubeId
                }).ToList() ?? new List<ArticleFileAggregateResult>()
            };

            return result;
        }
    }
}