import {
  Injectable,
  inject,
  signal,
  Signal,
  WritableSignal,
  effect,
} from '@angular/core';
import { MegaMenuItem } from 'primeng/api';
// import { ReadOnlyChannelService } from '@/api/holy-bless/channels/read-only-channel.service';
import { ChannelSource } from '@/proxy/holy-bless/enums/channel-source.enum';
// import { ReadOnlyChannelService } from '@/apis/holy-bless/channels/channel.service';
import type { ChannelTreeDto } from '@/apis/holy-bless/channels/dtos';
// import { ChannelSubDomain } from '@/apis/holy-bless/enums/channel-sub-domain.enum';
import { HttpResourceRef } from '@angular/common/http';
import { UserManifestService } from '@/commons/translate/user-manifest-service';

@Injectable({ providedIn: 'root' })
export class MenuService {
  // private channelService = inject(ReadOnlyChannelService);
  private userManifestService = inject(UserManifestService);
  /**
   * Signal holding the MegaMenuItems for the current channel tree.
   */

  private _menuItems: WritableSignal<MegaMenuItem[]> = signal([]);
  menuItems: Signal<MegaMenuItem[]> = this._menuItems.asReadonly();
  private _channelTreeRessource: HttpResourceRef<ChannelTreeDto[]> | undefined;
  private _channels: ChannelTreeDto[] = [];

  constructor() {
    // Load default language menu after DI is ready
    // this.loadMenu(this.userManifestService.userManifest.languageWritten);
  }
  /**
   * Loads the channel tree for the given language and updates the menuItems signal.
   */
  loadMenu(languageCode: Signal<string>) {
    // this._channelTreeRessource =
    // this.channelService.getChannelTree(languageCode);
    console.log(
      'MenuService: Loading channel tree for language API return',
      this._channelTreeRessource,
    );

    // Use effect on the resource's value signal directly to react to changes
    effect(() => {
      const channels = this._channelTreeRessource!.value();
      if (channels && channels.length > 0) {
        this._channels = channels;
        console.log('MenuService: Loaded channel tree', this._channels);
        const converted = this.convertChannelTreeToMenuItems(this._channels);
        console.log('MenuService: Loaded menu items', converted);
        this._menuItems.set(converted);
      } else {
        // Optionally clear menu if no channels
        this._menuItems.set([]);
      }
    });
  }

  /**
   * Converts a ChannelTreeDto[] to MegaMenuItem[]
   */
  private convertChannelTreeToMenuItems(
    channels: ChannelTreeDto[],
  ): MegaMenuItem[] {
    if (!channels) return [];
    return channels.map((channel) =>
      this.channelTreeDtoToMegaMenuItem(channel),
    );
  }

  /**
   * Recursively converts a ChannelTreeDto to a MegaMenuItem, setting routerLink based on ChannelSource
   */
  private channelTreeDtoToMegaMenuItem(channel: ChannelTreeDto): MegaMenuItem {
    return {
      label: channel.name,
      id: channel.id?.toString(),
      routerLink:
        channel.children && channel.children.length > 0
          ? undefined
          : this.getRouterLinkForChannel(channel),
      items:
        channel.children && channel.children.length > 0
          ? [
            channel.children.map((child: any) =>
              this.channelTreeDtoToMegaMenuItem(child),
            ),
          ]
          : undefined,
      // Add more mappings as needed, e.g. icon, etc.
    };
  }

  /**
   * Determines the routerLink for a channel based on its channelSource
   */
  private getRouterLinkForChannel(
    channel: ChannelTreeDto,
  ): string[] | undefined {
    if (
      channel.channelSource === undefined ||
      channel.channelSource === null
    )
      return undefined;
    switch (channel.channelSource) {
      case ChannelSource.Collection:
        return ['/main-site', channel.id?.toString()];
      case ChannelSource.Ebook:
        return ['/ebook', channel.id?.toString()];
      case ChannelSource.VirtualDisk:
        return ['/virtual-disk', channel.id?.toString()];
      default:
        return ['/channel', channel.id?.toString()];
    }
  }
}
