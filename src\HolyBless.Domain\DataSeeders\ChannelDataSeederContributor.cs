using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.Channels;
using HolyBless.Enums;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders
{
    public class ChannelDataSeederContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<Channel, int> _channelRepository;

        // Traditional Chinese translations for channel names
        private static readonly Dictionary<string, string> NameZhHantMap = new()
        {
            { "主站", "主站" },
            { "播客", "播客" },
            { "电子书", "電子書" },
            { "网盘", "網盤" },
            { "《梦》系列", "《夢》系列" },
            { "天门开", "天門開" },
            { "爱的温暖", "愛的溫暖" },
            { "心灵综述", "心靈綜述" },
            { "生死河", "生死河" },
            { "生命之光", "生命之光" },
            { "平安赐福", "平安賜福" },
            { "修行答疑", "修行答疑" },
            { "博客", "博客" },
            { "微博", "微博" },
            { "回头是岸", "回頭是岸" },
            { "忆念老师", "憶念老師" },
            { "修行心得", "修行心得" },
            { "温故知新", "溫故知新" },
            { "虔诚忏悔", "虔誠懺悔" },
            { "神迹之光", "神蹟之光" },
            { "原声亲诵", "原聲親誦" },
            { "灵性创作", "靈性創作" },
            { "浮生寻归", "浮生尋歸" },
            { "专题", "專題" },
            { "书籍", "書籍" },
            { "汇编", "彙編" },
            { "小册子", "小冊子" },
            { "原声", "原聲" },
            { "灌卡专用", "灌卡專用" },
            { "配音视频", "配音視頻" },
            { "文档", "文檔" },
            { "音频", "音頻" },
            { "视频", "視頻" },
        };

        // English translations for channel names
        private static readonly Dictionary<string, string> NameEnMap = new()
        {
            { "主站", "Main Site" },
            { "播客", "Podcast" },
            { "电子书", "E-Book" },
            { "网盘", "Cloud Drive" },
            { "《梦》系列", "Dream Series" },
            { "天门开", "Heaven's Gate" },
            { "爱的温暖", "Warmth of Love" },
            { "心灵综述", "Spiritual Overview" },
            { "生死河", "River of Life and Death" },
            { "生命之光", "Light of Life" },
            { "平安赐福", "Blessing of Peace" },
            { "修行答疑", "Practice Q&A" },
            { "博客", "Blog" },
            { "微博", "Weibo" },
            { "回头是岸", "Turn Back to the Shore" },
            { "忆念老师", "Remembering the Teacher" },
            { "修行心得", "Practice Insights" },
            { "温故知新", "Review and Learn" },
            { "虔诚忏悔", "Sincere Repentance" },
            { "神迹之光", "Light of Miracles" },
            { "原声亲诵", "Original Recitation" },
            { "灵性创作", "Spiritual Creation" },
            { "浮生寻归", "Seeking Home in Life" },
            { "专题", "Special Topic" },
            { "书籍", "Book" },
            { "汇编", "Compilation" },
            { "小册子", "Booklet" },
            { "原声", "Original Sound" },
            { "灌卡专用", "Card Dubbing" },
            { "配音视频", "Dubbed Video" },
            { "文档", "Document" },
            { "音频", "Audio" },
            { "视频", "Video" },
        };

        public static Channel[] ChannelList =>
        [
            new Channel(1)
            {
                Name = "主站",
                Weight = 1,
                ParentChannelId = null,
                LanguageCode = "zh-Hans",
                ContentCode = "main",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(2)
            {
                Name = "播客",
                Weight = 2,
                ParentChannelId = null,
                LanguageCode = "zh-Hans",
                ContentCode = "podcast",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(3)
            {
                Name = "电子书",
                Weight = 3,
                ParentChannelId = null,
                LanguageCode = "zh-Hans",
                ContentCode = "ebook",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(4)
            {
                Name = "网盘",
                Weight = 4,
                ParentChannelId = null,
                LanguageCode = "zh-Hans",
                ContentCode = "virtualdisk",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(11)
            {
                Name = "《梦》系列",
                Weight = 1,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-dream",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(12)
            {
                Name = "天门开",
                Weight = 2,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-tmk",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(13)
            {
                Name = "爱的温暖",
                Weight = 3,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-love",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(14)
            {
                Name = "心灵综述",
                Weight = 4,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-xlzs",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(15)
            {
                Name = "生死河",
                Weight = 5,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-ssriver",
                ChannelSource = ChannelSource.Collection
            },

            new Channel(16)
            {
                Name = "生命之光",
                Weight = 6,
                ParentChannelId = 1,
                LanguageCode = "zh-Hans",
                ContentCode = "1-smzg",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(21)
            {
                Name = "天门开",
                Weight = 1,
                ParentChannelId = 12,
                LanguageCode = "zh-Hans",
                ContentCode = "12-tmk",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(22)
            {
                Name = "平安赐福",
                Weight = 2,
                ParentChannelId = 12,
                LanguageCode = "zh-Hans",
                ContentCode = "12-pacf",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(41)
            {
                Name = "心灵综述",
                Weight = 1,
                ParentChannelId = 14,
                LanguageCode = "zh-Hans",
                ContentCode = "14-xlzs",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(42)
            {
                Name = "修行答疑",
                Weight = 2,
                ParentChannelId = 14,
                LanguageCode = "zh-Hans",
                ContentCode = "14-qa",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(51)
            {
                Name = "博客",
                Weight = 1,
                ParentChannelId = 15,
                LanguageCode = "zh-Hans",
                ContentCode = "15-blog",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(52)
            {
                Name = "微博",
                Weight = 2,
                ParentChannelId = 15,
                LanguageCode = "zh-Hans",
                ContentCode = "15-weibo",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(61)
            {
                Name = "回头是岸",
                Weight = 1,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-htsa",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(62)
            {
                Name = "忆念老师",
                Weight = 2,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-ynls",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(63)
            {
                Name = "修行心得",
                Weight = 3,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-xxxd",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(64)
            {
                Name = "温故知新",
                Weight = 4,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-wgzx",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(65)
            {
                Name = "虔诚忏悔",
                Weight = 5,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-qccw",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(66)
            {
                Name = "神迹之光",
                Weight = 6,
                ParentChannelId = 16,
                LanguageCode = "zh-Hans",
                ContentCode = "16-sjzg",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(201)
            {
                Name = "主站",
                Weight = 1,
                ParentChannelId = 2,
                LanguageCode = "zh-Hans",
                ContentCode = "2-main",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(202)
            {
                Name = "生命之光",
                Weight = 2,
                ParentChannelId = 2,
                LanguageCode = "zh-Hans",
                ContentCode = "2-smzg",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(211)
            {
                Name = "爱的温暖",
                Weight = 1,
                ParentChannelId = 201,
                LanguageCode = "zh-Hans",
                ContentCode = "201-love",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(212)
            {
                Name = "原声亲诵",
                Weight = 2,
                ParentChannelId = 201,
                LanguageCode = "zh-Hans",
                ContentCode = "201-ysqs",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(213)
            {
                Name = "灵性创作",
                Weight = 3,
                ParentChannelId = 201,
                LanguageCode = "zh-Hans",
                ContentCode = "201-lxcz",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(221)
            {
                Name = "神迹之光",
                Weight = 1,
                ParentChannelId = 202,
                LanguageCode = "zh-Hans",
                ContentCode = "202-sjzg",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(222)
            {
                Name = "浮生寻归",
                Weight = 2,
                ParentChannelId = 202,
                LanguageCode = "zh-Hans",
                ContentCode = "202-fsxg",
                ChannelSource = ChannelSource.Collection
            },
            new Channel(301)
            {
                Name = "专题",
                Weight = 1,
                ParentChannelId = 3,
                LanguageCode = "zh-Hans",
                ContentCode = "3-topic",
                ChannelSource = ChannelSource.Ebook
            }
            ,
            new Channel(302)
            {
                Name = "生命之光",
                Weight = 1,
                ParentChannelId = 3,
                LanguageCode = "zh-Hans",
                ContentCode = "3-smzg",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(311)
            {
                Name = "书籍",
                Weight = 1,
                ParentChannelId = 301,
                LanguageCode = "zh-Hans",
                ContentCode = "301-book",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(312)
            {
                Name = "汇编",
                Weight = 2,
                ParentChannelId = 301,
                LanguageCode = "zh-Hans",
                ContentCode = "301-hb",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(313)
            {
                Name = "小册子",
                Weight = 3,
                ParentChannelId = 301,
                LanguageCode = "zh-Hans",
                ContentCode = "301-xcz",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(401)
            {
                Name = "原声",
                Weight = 1,
                ParentChannelId = 4,
                LanguageCode = "zh-Hans",
                ContentCode = "4-original",
                ChannelSource = ChannelSource.VirtualDisk
            },
            new Channel(402)
            {
                Name = "灌卡专用",
                Weight = 2,
                ParentChannelId = 4,
                LanguageCode = "zh-Hans",
                ContentCode = "4-gkzy",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(403)
            {
                Name = "配音视频",
                Weight = 3,
                ParentChannelId = 4,
                LanguageCode = "zh-Hans",
                ContentCode = "4-pysj",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(411)
            {
                Name = "文档",
                Weight = 1,
                ParentChannelId = 401,
                LanguageCode = "zh-Hans",
                ContentCode = "401-doc",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(412)
            {
                Name = "音频",
                Weight = 2,
                ParentChannelId = 401,
                LanguageCode = "zh-Hans",
                ContentCode = "401-audio",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(413)
            {
                Name = "视频",
                Weight = 3,
                ParentChannelId = 401,
                LanguageCode = "zh-Hans",
                ContentCode = "401-video",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(421)
            {
                Name = "音频",
                Weight = 1,
                ParentChannelId = 402,
                LanguageCode = "zh-Hans",
                ContentCode = "402-audio",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(422)
            {
                Name = "视频",
                Weight = 2,
                ParentChannelId = 402,
                LanguageCode = "zh-Hans",
                ContentCode = "402-video",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(431)
            {
                Name = "音频",
                Weight = 1,
                ParentChannelId = 403,
                LanguageCode = "zh-Hans",
                ContentCode = "403-audio",
                ChannelSource = ChannelSource.Ebook
            },
            new Channel(432)
            {
                Name = "视频",
                Weight = 3,
                ParentChannelId = 403,
                LanguageCode = "zh-Hans",
                ContentCode = "403-video",
                ChannelSource = ChannelSource.Ebook
            }
        ];

        private static readonly Channel[] ChannelHantList = ChannelList.Select(static c =>
           new Channel(c.Id + 1000)
           {
               Name = NameZhHantMap.TryGetValue(c.Name, out var hant) ? hant : c.Name,
               Weight = c.Weight,
               ParentChannelId = c.ParentChannelId.HasValue ? c.ParentChannelId + 1000 : null,
               LanguageCode = "zh-Hant",
               ContentCode = c.ContentCode,
               ChannelSource = c.ChannelSource
           }
        ).ToArray();

        private static readonly Channel[] ChannelEnList = ChannelList.Select(static c =>
           new Channel(c.Id + 2000)
           {
               Name = NameEnMap.TryGetValue(c.Name, out var hant) ? hant : c.Name,
               Weight = c.Weight,
               ParentChannelId = c.ParentChannelId.HasValue ? c.ParentChannelId + 2000 : null,
               LanguageCode = "en",
               ContentCode = c.ContentCode,
               ChannelSource = c.ChannelSource
           }
        ).ToArray();

        public ChannelDataSeederContributor(IRepository<Channel, int> channelRepository)
        {
            _channelRepository = channelRepository;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            // Delete all existing channels first
            var allChannels = await _channelRepository.GetListAsync();
            if (allChannels.Any())
            {
                return;
                //await _channelRepository.HardDeleteAsync(allChannels, autoSave: true);
            }
            // Insert new channels
            await _channelRepository.InsertManyAsync(ChannelList, autoSave: true);
            await _channelRepository.InsertManyAsync(ChannelHantList, autoSave: true);
            await _channelRepository.InsertManyAsync(ChannelEnList, autoSave: true);
        }
    }
}