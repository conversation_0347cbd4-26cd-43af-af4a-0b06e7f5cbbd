/* You can add global styles to this file, and also import other style files */
@use './tailwind.css';
@use '@shared-theme/layout.scss';
@use 'primeicons/primeicons.css';
@use './assets/demo/demo.scss';
@use './assets/styles/preloading.css';
@use '@fortawesome/fontawesome-free/css/all.min.css';

// 覆盖所有PrimeNG组件的圆角
.p-component,
.p-button,
.p-card,
.p-datatable,
.p-inputtext,
.p-dropdown,
.p-tag,
.card {
  border-radius: 3px !important;
}

.p-tree,
.p-card,
.card,
.content-area {
  background-color: unset !important;
  box-shadow: none !important;
}

.p-card-content {
  border-bottom: 1px solid #e0e0e0;

  @media (min-width: 1200px) {
    border-right: 1px solid #e0e0e0;
    border-bottom: none;
  }
}

// 特殊处理表格单元格
.p-datatable .p-datatable-thead>tr>th,
.p-datatable .p-datatable-tbody>tr>td {
  border-radius: 0 !important; // 表格单元格取消圆角
}