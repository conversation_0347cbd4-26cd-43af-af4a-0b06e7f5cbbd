﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class SubDomainchangesBucket : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsStagingBucket",
                table: "Buckets");

            migrationBuilder.RenameColumn(
                name: "Domain",
                table: "Buckets",
                newName: "SubDomain");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SubDomain",
                table: "Buckets",
                newName: "Domain");

            migrationBuilder.AddColumn<bool>(
                name: "IsStagingBucket",
                table: "Buckets",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
