using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Lookups.Dtos
{
    public class CreateUpdateCountryDto
    {
        [Required]
        public string Name { get; set; } = default!;

        [Required]
        public string Code { get; set; } = default!;

        [Required]
        public string Code3 { get; set; } = default!;

        public string DefaultLangCode { get; set; } = LangCode.English;
        public string DefaultSpokenLangCode { get; set; } = SpokenLangCode.English;
    }
}