﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
<system.webServer>
 <rewrite>
  <rules>
    <rule name="Redirect" stopProcessing="true">
      <match url="getEnvConfig" />
      <action type="Redirect" url="dynamic-env.json" />
    </rule>
    <rule name="Angular Routes" stopProcessing="true">
      <match url=".*" />
      <conditions logicalGrouping="MatchAll">
        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
      </conditions>
      <action type="Rewrite" url="./index.html" />
    </rule>
  </rules>
 </rewrite>
</system.webServer>
</configuration>