using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Articles.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Tags;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Articles
{
    [Authorize(HolyBlessPermissions.Articles.Default)]
    public class ArticleAppService : ReadOnlyArticleAppService, IArticleAppService
    {
        public ArticleAppService(
            IRepository<Article, int> repository,
            IRepository<ArticleToTag> articleToTagRepository,
            IRepository<Tag, int> tagRepository,
            IRepository<TeacherArticleLink> teacherArticleLinkRepository,
            IRepository<ArticleFile, int> articleFileRepository,
            IRepository<BucketFile, int> bucketFileRepository)
            : base(repository, articleToTagRepository, tagRepository, teacherArticleLinkRepository, articleFileRepository, bucketFileRepository)
        {
        }

        [Authorize(HolyBlessPermissions.Articles.Create)]
        public async Task<ArticleDto> CreateAsync(CreateUpdateArticleDto input)
        {
            var article = ObjectMapper.Map<CreateUpdateArticleDto, Article>(input);
            article = await _repository.InsertAsync(article, autoSave: true);
            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task<ArticleDto> UpdateAsync(int id, CreateUpdateArticleDto input)
        {
            var article = await _repository.GetAsync(id);
            ObjectMapper.Map(input, article);
            article = await _repository.UpdateAsync(article, autoSave: true);
            return ObjectMapper.Map<Article, ArticleDto>(article);
        }

        [Authorize(HolyBlessPermissions.Articles.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id);
            var articleTags = await _articleToTagRepository.GetListAsync(x => x.ArticleId == id);
            if (articleTags.Count > 0)
            {
                await _articleToTagRepository.DeleteManyAsync(articleTags);
            }
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task<List<ArticleToTagDto>> AddTagsAsync(int articleId, List<int> tagIds)
        {
            // Verify article exists
            var article = await _repository.GetAsync(articleId);
            if (article == null)
            {
                throw new EntityNotFoundException(typeof(Article), articleId);
            }

            // Verify all tags exist
            var existingTagIds = await _tagRepository.GetListAsync();
            var validTagIds = existingTagIds.Select(t => t.Id).ToList();
            var invalidTagIds = tagIds.Where(id => !validTagIds.Contains(id)).ToList();

            if (invalidTagIds.Any())
            {
                throw new UserFriendlyException($"Invalid tag IDs: {string.Join(", ", invalidTagIds)}");
            }

            var articleTags = tagIds.Select(tagId => new ArticleToTag
            {
                ArticleId = articleId,
                TagId = tagId
            }).ToList();

            // Check for existing tags to avoid duplicates
            var queryable = await _articleToTagRepository.GetQueryableAsync();
            var existingTags = await queryable
                .Where(x => x.ArticleId == articleId && tagIds.Contains(x.TagId))
                .ToListAsync();

            // Filter out existing tags
            articleTags = articleTags
                .Where(at => !existingTags.Any(et => et.TagId == at.TagId))
                .ToList();

            if (articleTags.Any())
            {
                await _articleToTagRepository.InsertManyAsync(articleTags, autoSave: true);
            }

            return ObjectMapper.Map<List<ArticleToTag>, List<ArticleToTagDto>>(articleTags);
        }

        public async Task RemoveTagsFromArticle(int articleId, List<int> tagIds)
        {
            var articleTags = await _articleToTagRepository.GetListAsync(
                x => x.ArticleId == articleId && tagIds.Contains(x.TagId));

            await _articleToTagRepository.DeleteManyAsync(articleTags);
        }

        [Authorize(HolyBlessPermissions.Articles.Edit)]
        public async Task AddTeacherArticleLinks(int studentArticleId, List<TeacherArticleLinkDto> teacherLinks)
        {
            // Get all existing links for this student article
            var existingLinks = await _teacherArticleLinkRepository.GetListAsync(x => x.StudentArticleId == studentArticleId);
            foreach (var linkDto in teacherLinks)
            {
                var existing = existingLinks.FirstOrDefault(x => x.TeacherArticleId == linkDto.TeacherArticleId);
                if (existing == null)
                {
                    await _teacherArticleLinkRepository.InsertAsync(new TeacherArticleLink
                    {
                        StudentArticleId = studentArticleId,
                        TeacherArticleId = linkDto.TeacherArticleId,
                        Weight = linkDto.Weight
                    }, autoSave: true);
                }
                else
                {
                    existing.Weight = linkDto.Weight;
                    await _teacherArticleLinkRepository.UpdateAsync(existing, autoSave: true);
                }
            }
        }
    }
}