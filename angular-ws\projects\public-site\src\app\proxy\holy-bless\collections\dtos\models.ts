import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { BucketFileDto } from '../../buckets/dtos/models';
import type { ListStyle } from '../../enums/list-style.enum';
import type { DefaultOrderByField } from '../../enums/default-order-by-field.enum';

export interface CollectionArticleSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
  status?: PublishStatus;
}

export interface CollectionDto extends EntityDto<number> {
  parentCollectionId?: number;
  contentCode?: string;
  languageCode?: string;
  thumbnailFileId?: number;
  thumbnailBucketFile: BucketFileDto;
  name?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  listStyle?: ListStyle;
  renderAsOneSet: boolean;
  status?: PublishStatus;
  memo?: string;
  defaultOrderBy?: DefaultOrderByField;
}

export interface CollectionFileSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
}

export interface CollectionSearchDto extends PagedAndSortedResultRequestDto {
  status?: PublishStatus;
  channelId?: number;
}

export interface CollectionToArticleDto {
  articleId: number;
  weight: number;
}

export interface CollectionToFileDto {
  fileId: number;
  weight: number;
}
