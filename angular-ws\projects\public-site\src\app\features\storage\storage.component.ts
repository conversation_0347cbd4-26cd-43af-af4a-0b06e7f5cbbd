import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-storage',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="storage-container min-h-screen bg-gray-50">
      <!-- 导航栏 -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a routerLink="/home" class="text-2xl font-bold text-blue-600">HolyBless</a>
              <span class="ml-4 text-gray-500">/ 网盘</span>
            </div>
            <div class="flex items-center space-x-6">
              <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                上传文件
              </button>
              <a routerLink="/search" class="text-gray-700 hover:text-blue-600 font-medium">搜索</a>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 页面标题和工具栏 -->
        <div class="flex justify-between items-center mb-8">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">我的网盘</h1>
            <p class="text-gray-600">安全存储和管理您的文件</p>
          </div>
          <div class="flex space-x-3">
            <button class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              新建文件夹
            </button>
            <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              批量上传
            </button>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 侧边栏 -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">存储空间</h3>
              <div class="mb-4">
                <div class="flex justify-between text-sm mb-2">
                  <span class="text-gray-600">已使用</span>
                  <span class="font-medium">2.4 GB / 10 GB</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-blue-600 h-2 rounded-full" style="width: 24%"></div>
                </div>
              </div>
              
              <div class="space-y-2">
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-blue-600 mr-3">📁</span>
                    <span class="text-sm">全部文件</span>
                  </div>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-green-600 mr-3">🖼️</span>
                    <span class="text-sm">图片</span>
                  </div>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-red-600 mr-3">📄</span>
                    <span class="text-sm">文档</span>
                  </div>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-purple-600 mr-3">🎵</span>
                    <span class="text-sm">音频</span>
                  </div>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <div class="flex items-center">
                    <span class="text-orange-600 mr-3">🗑️</span>
                    <span class="text-sm">回收站</span>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <!-- 主要内容区域 -->
          <div class="lg:col-span-3">
            <div class="bg-white rounded-lg shadow-sm">
              <!-- 文件列表头部 -->
              <div class="flex items-center justify-between p-4 border-b">
                <div class="flex items-center space-x-4">
                  <button class="text-gray-400 hover:text-gray-600">
                    <span class="text-lg">📋</span>
                  </button>
                  <button class="text-gray-400 hover:text-gray-600">
                    <span class="text-lg">⊞</span>
                  </button>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">排序：</span>
                  <select class="border border-gray-300 rounded px-2 py-1 text-sm">
                    <option>名称</option>
                    <option>修改时间</option>
                    <option>大小</option>
                  </select>
                </div>
              </div>

              <!-- 文件列表 -->
              <div class="p-4">
                <div class="space-y-2">
                  <!-- 文件夹 -->
                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer" (click)="openFolder('documents')">
                    <span class="text-blue-500 text-xl mr-4">📁</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">文档</h3>
                      <p class="text-xs text-gray-600">12 个文件</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-28</div>
                  </div>

                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer" (click)="openFolder('images')">
                    <span class="text-blue-500 text-xl mr-4">📁</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">图片</h3>
                      <p class="text-xs text-gray-600">45 个文件</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-25</div>
                  </div>

                  <!-- 文件 -->
                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                    <span class="text-red-500 text-xl mr-4">📄</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">项目报告.pdf</h3>
                      <p class="text-xs text-gray-600">2.4 MB</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-30</div>
                  </div>

                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                    <span class="text-green-500 text-xl mr-4">📊</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">数据分析.xlsx</h3>
                      <p class="text-xs text-gray-600">1.8 MB</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-29</div>
                  </div>

                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                    <span class="text-purple-500 text-xl mr-4">🎵</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">背景音乐.mp3</h3>
                      <p class="text-xs text-gray-600">5.2 MB</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-27</div>
                  </div>

                  <div class="flex items-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                    <span class="text-orange-500 text-xl mr-4">🖼️</span>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">设计图.png</h3>
                      <p class="text-xs text-gray-600">3.1 MB</p>
                    </div>
                    <div class="text-xs text-gray-500">2024-06-26</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class StorageComponent {
  openFolder(folderId: string) {
    console.log('Opening folder:', folderId);
    // 导航到文件夹页面
  }
}
