name: FE - Dev Build Angular Workspace

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'angular-ws/**'
      - '.github/workflows/fe-dev-ci.yml'
      - '.github/workflows/fe-cd-public-site.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'angular-ws/**'
      - '.github/workflows/fe-dev-ci.yml'
      - '.github/workflows/fe-cd-public-site.yml'

jobs:
  build-angular:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: angular-ws

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Use Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Install Angular CLI
        run: yarn global add @angular/cli

      - name: Install dependencies
        run: yarn install

      - name: Build public-site
        run: npx ng build public-site

      - name: Build admin-site
        run: npx ng build admin-site