import type { AuditedEntityDto, EntityDto } from '@abp/ng.core';
import type { ArticleContentCategory } from '../../enums/article-content-category.enum';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentType } from '../../enums/content-type.enum';

export interface ArticleDto extends AuditedEntityDto<number> {
  contentCodeId: number;
  languageCode?: string;
  title?: string;
  thumbnailFileId?: number;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  articleContentCategory?: ArticleContentCategory;
  publishDate?: string;
  status?: PublishStatus;
  content?: string;
  memo?: string;
  articleFiles: ArticleFileDto[];
}

export interface ArticleFileDto extends EntityDto<number> {
  articleId: number;
  articleTitle?: string;
  fileId: number;
  fileName?: string;
  mediaType?: MediaType;
  contentType?: ContentType;
  title?: string;
  description?: string;
  isPrimary: boolean;
}

export interface TeacherArticleLinkDto {
  teacherArticleId: number;
  weight: number;
}
