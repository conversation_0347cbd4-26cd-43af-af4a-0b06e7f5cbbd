using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.StorageProviders
{
    public class CreateUpdateStorageProviderDto
    {
        [Required]
        public string ProviderName { get; set; } = default!;

        [Required]
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;

        public List<int> PreferCountryIds { get; set; } = new List<int>();
        public string? Description { get; set; }
        public string BindedDomain { get; set; } = "";
    }
}