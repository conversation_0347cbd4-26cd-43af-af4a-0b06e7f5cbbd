import { Injectable, signal } from '@angular/core';

export interface DrawerConfig {
  type: 'settings' | 'playlist' | 'custom';
  title: string;
  width?: string;
  position?: 'left' | 'right' | 'top' | 'bottom';
  modal?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class DrawerService {
  // 使用 signal 管理 drawer 状态
  private _isVisible = signal(false);
  private _config = signal<DrawerConfig>({
    type: 'settings',
    title: '设置',
    width: '25rem',
    position: 'right',
    modal: true
  });

  // 只读的 computed signals
  public readonly isVisible = this._isVisible.asReadonly();
  public readonly config = this._config.asReadonly();

  /**
   * 打开设置 drawer
   */
  openSettings(): void {
    this._config.set({
      type: 'settings',
      title: '设置',
      width: '25rem',
      position: 'right',
      modal: true
    });
    this._isVisible.set(true);
    this.preventBodyScroll();
  }

  /**
   * 打开播放列表 drawer
   */
  openPlaylist(): void {
    this._config.set({
      type: 'playlist',
      title: '播放列表',
      width: '30rem',
      position: 'right',
      modal: true
    });
    this._isVisible.set(true);
    this.preventBodyScroll();
  }

  /**
   * 打开自定义 drawer
   */
  openCustom(config: Partial<DrawerConfig>): void {
    this._config.set({
      type: 'custom',
      title: '自定义',
      width: '25rem',
      position: 'right',
      modal: true,
      ...config
    });
    this._isVisible.set(true);
    this.preventBodyScroll();
  }

  /**
   * 关闭 drawer
   */
  close(): void {
    this._isVisible.set(false);
    this.restoreBodyScroll();
  }

  /**
   * 切换 drawer 状态
   */
  toggle(): void {
    if (this._isVisible()) {
      this.close();
    } else {
      this.openSettings(); // 默认打开设置
    }
  }

  /**
   * 检查当前 drawer 类型
   */
  isType(type: DrawerConfig['type']): boolean {
    return this._config().type === type;
  }

  /**
   * 防止背景滚动
   */
  private preventBodyScroll(): void {
    document.body.style.overflow = 'hidden';
  }

  /**
   * 恢复背景滚动
   */
  private restoreBodyScroll(): void {
    document.body.style.overflow = '';
  }
}
