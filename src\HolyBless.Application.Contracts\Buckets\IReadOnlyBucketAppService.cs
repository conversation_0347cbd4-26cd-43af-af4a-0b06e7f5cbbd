using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Buckets
{
    public interface IReadOnlyBucketAppService : IApplicationService
    {
        Task<List<BucketDto>> GetBucketsByProviderIdAsync(int providerId);
        Task<List<BucketDto>> GetBucketsByLanguageAsync(string languageCode);
        Task<BucketDto> GetBucketByNameAsync(string bucketName);
        Task<List<BucketDto>> GetAllBucketsAsync();
    }
}
