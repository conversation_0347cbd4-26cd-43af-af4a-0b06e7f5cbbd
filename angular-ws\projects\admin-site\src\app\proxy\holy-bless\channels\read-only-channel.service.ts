import type { ChannelDto, ChannelSearchDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyChannelService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: ChannelSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ChannelDto>>({
      method: 'GET',
      url: '/api/app/read-only-channel',
      params: { languageCode: input.languageCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
