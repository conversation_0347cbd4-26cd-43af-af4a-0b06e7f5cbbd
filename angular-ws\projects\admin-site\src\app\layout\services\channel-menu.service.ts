import { Injectable } from '@angular/core';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels/read-only-channel.service';
import { ChannelDto, ChannelSearchDto } from '@/proxy/holy-bless/channels/dtos/models';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';

export interface MenuItem {
  label: string;
  routerLink?: string[];
  items?: MenuItem[];
}

@Injectable({
  providedIn: 'root'
})
export class ChannelMenuService {
  constructor(private channelService: ReadOnlyChannelService) {}

  getMenu(languageCode?: string): Observable<MenuItem[]> {
    const input: ChannelSearchDto = {
      languageCode,
      skipCount: 0,
      maxResultCount: 1000 // adjust as needed
    };

    return this.channelService.getList(input).pipe(
      map(result => this.buildMenu(result.items || []))
    );
  }

  private buildMenu(channels: ChannelDto[]): MenuItem[] {
    // Build a map for quick lookup
    const mapById = new Map<number, MenuItem & { id: number, parentChannelId?: number }>();
    const roots: MenuItem[] = [];

    // First, create all menu items
    channels.forEach(channel => {
      if (channel.id !== undefined) {
        mapById.set(channel.id, {
          id: channel.id,
          parentChannelId: channel.parentChannelId,
          label: channel.channelName || `Channel ${channel.id}`,
          routerLink: ['/channels', channel.id.toString()],
          items: []
        });
      }
    });

    // Then, build the hierarchy (max 3 levels)
    mapById.forEach(item => {
      if (item.parentChannelId && mapById.has(item.parentChannelId)) {
        const parent = mapById.get(item.parentChannelId)!;
        if (this.getDepth(parent, mapById) < 2) { // 0=root, 1=child, 2=grandchild
          parent.items!.push(item);
        }
      } else {
        roots.push(item);
      }
    });

    // Remove helper properties before returning
    const strip = (item: any): MenuItem => ({
      label: item.label,
      routerLink: item.routerLink,
      items: item.items && item.items.length ? item.items.map(strip) : undefined
    });

    return roots.map(strip);
  }

  private getDepth(item: any, mapById: Map<number, any>): number {
    let depth = 0;
    let current = item;
    while (current.parentChannelId && mapById.has(current.parentChannelId)) {
      depth++;
      current = mapById.get(current.parentChannelId);
      if (depth > 2) break;
    }
    return depth;
  }
}