name: SonarCloud Analysis

on:
  workflow_run:
    workflows:
      - 'BE - Dev Build and Test'
      - 'FE - Dev Build Angular Workspace'
    types:
      - completed
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  analyze-backend:
    name: Analyze Backend
    if: ${{ github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.name == 'BE - Dev Build and Test' }}
    runs-on: ubuntu-latest
    
    steps:
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: 17
          distribution: 'zulu'

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '9.0.x'

      - name: Install SonarCloud scanner
        run: |
          dotnet tool install --global dotnet-sonarscanner
          dotnet tool install --global dotnet-coverage

      - name: Cache SonarCloud packages
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Build and analyze
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        shell: bash
        run: |
          dotnet sonarscanner begin \
            /k:"holybless-backend" \
            /o:"${{ secrets.SONAR_ORGANIZATION }}" \
            /d:sonar.token="${{ secrets.SONAR_TOKEN }}" \
            /d:sonar.host.url="https://sonarcloud.io" \
            /s:"${GITHUB_WORKSPACE}/sonar-project-backend.properties"
          
          dotnet build HolyBless.sln --no-incremental
          dotnet test --no-build --collect:"XPlat Code Coverage" --settings coverlet.runsettings
          
          dotnet sonarscanner end /d:sonar.token="${{ secrets.SONAR_TOKEN }}"
  analyze-frontend:
    name: Analyze Frontend
    if: ${{ github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.name == 'FE - Dev Build Angular Workspace' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: angular-ws

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'yarn'
          cache-dependency-path: angular-ws/yarn.lock

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run Tests with Coverage
        run: |
          yarn test:public-site
          yarn test:admin-site
          yarn test:shared-lib

      - name: Install SonarCloud scanner
        run: yarn global add sonarqube-scanner

      - name: Run SonarCloud Analysis
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN_FE }}
        shell: bash
        run: |
          sonar-scanner \
            -Dsonar.projectKey="holybless-frontend" \
            -Dsonar.organization="${{ secrets.SONAR_ORGANIZATION }}" \
            -Dsonar.host.url="https://sonarcloud.io" \
            -Dsonar.token="${{ secrets.SONAR_TOKEN }}" \
            -Dsonar.projectBaseDir="${GITHUB_WORKSPACE}/angular-ws" \
            -Dsonar.configFile=sonar-project.properties
