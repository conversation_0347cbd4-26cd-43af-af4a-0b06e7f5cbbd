using System;
using Volo.Abp.Application.Dtos;
using HolyBless.Enums;

namespace HolyBless.Articles.Dtos
{
    public class ArticleFileDto : EntityDto<int>
    {
        public int ArticleId { get; set; }
        public string ArticleTitle { get; set; } = string.Empty;

        public int FileId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public MediaType MediaType { get; set; }
        public ContentCategory ContentType { get; set; }

        public string? Title { get; set; }
        public string? Description { get; set; }
        public bool IsPrimary { get; set; }
    }
}