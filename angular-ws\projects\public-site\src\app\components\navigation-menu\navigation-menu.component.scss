// 语言选择区域的图标样式
.language-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1.5rem;
  line-height: 1;
  font-size: 0.875rem; // 14px
}

// 确保语言按钮样式一致
.language-button {
  ::ng-deep .p-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

// 整个语言选择容器的对齐
.language-selector {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  height: 2rem; // 确保容器有固定高度
}

// 右侧控制区域的对齐
.control-area {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  height: 2.5rem; // 确保整个区域有统一高度
}
