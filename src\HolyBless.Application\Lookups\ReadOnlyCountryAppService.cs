using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using System.Linq.Dynamic.Core;
using HolyBless.Lookups.Dtos;
using Volo.Abp;
using Microsoft.Extensions.Caching.Memory;

namespace HolyBless.Lookups
{
    public class ReadOnlyCountryAppService : ApplicationService, IReadOnlyCountryAppService
    {
        protected readonly IRepository<Country, int> _repository;
        protected readonly IMemoryCache _memoryCache;
        protected static readonly string KeyCountry = "country_cache";
        protected readonly MemoryCacheEntryOptions _memoryCacheEntryOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(600),
            SlidingExpiration = TimeSpan.FromMinutes(60),
            Priority = CacheItemPriority.High
        };

        public ReadOnlyCountryAppService(IRepository<Country, int> repository, IMemoryCache memoryCache)
        {
            _repository = repository;
            _memoryCache = memoryCache;
        }

        protected async Task<List<Country>> GetCachedAllCountries()
        {
            if (!_memoryCache.TryGetValue(KeyCountry, out List<Country>? countries))
            {
                var queryable = await _repository.GetQueryableAsync();
                var query = queryable.OrderBy(x => x.Name);
                countries = await AsyncExecuter.ToListAsync(query);
                if (countries == null || countries.Count == 0)
                {
                    throw new BusinessException("Could not find any countries");
                }
                _memoryCache.Set(KeyCountry, countries, _memoryCacheEntryOptions);
            }
            return countries ?? [];
        }

        public async Task<CountryDto> GetAsync(int id)
        {
            var allCountries = await GetCachedAllCountries();
            var country = allCountries.FirstOrDefault(x => x.Id == id);
            if (country == null)
            {
                throw new BusinessException($"Could not find country with id: {id}");
            }
            return ObjectMapper.Map<Country, CountryDto>(country);
        }

        public async Task<PagedResultDto<CountryDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var allCountries = await GetCachedAllCountries();
            var query = allCountries.AsQueryable()
                .OrderBy(input.Sorting ?? "Name")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var countries = query.ToList();
            var totalCount = allCountries.Count;

            return new PagedResultDto<CountryDto>(
                totalCount,
                ObjectMapper.Map<List<Country>, List<CountryDto>>(countries)
            );
        }
    }
}