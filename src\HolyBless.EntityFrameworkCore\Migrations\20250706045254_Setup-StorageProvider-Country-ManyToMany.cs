﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class SetupStorageProviderCountryManyToMany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PreferCountry",
                table: "StorageProviders");

            migrationBuilder.AddColumn<string>(
                name: "DefaultLangCode",
                table: "Countries",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DefaultSpokenLangCode",
                table: "Countries",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SpokenLangCode",
                table: "Buckets",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "StorageProviderToCountries",
                columns: table => new
                {
                    StorageProviderId = table.Column<int>(type: "integer", nullable: false),
                    CountryId = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StorageProviderToCountries", x => new { x.StorageProviderId, x.CountryId });
                    table.ForeignKey(
                        name: "FK_StorageProviderToCountries_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StorageProviderToCountries_StorageProviders_StorageProvider~",
                        column: x => x.StorageProviderId,
                        principalTable: "StorageProviders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StorageProviderToCountries_CountryId",
                table: "StorageProviderToCountries",
                column: "CountryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StorageProviderToCountries");

            migrationBuilder.DropColumn(
                name: "DefaultLangCode",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "DefaultSpokenLangCode",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "SpokenLangCode",
                table: "Buckets");

            migrationBuilder.AddColumn<string>(
                name: "PreferCountry",
                table: "StorageProviders",
                type: "character varying(3)",
                unicode: false,
                maxLength: 3,
                nullable: true);
        }
    }
}
