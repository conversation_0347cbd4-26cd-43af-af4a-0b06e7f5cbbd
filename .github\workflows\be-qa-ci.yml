name: BE - QA Build and Test

on:
  push:
    branches: [ qa ]
    paths:
      - 'src/**'
      - 'test/**'
      - '*.props'
      - '*.sln'
  pull_request:
    branches: [ qa ]
    paths:
      - 'src/**'
      - 'test/**'
      - '*.props'
      - '*.sln'
  workflow_dispatch:

env:
  DOTNET_VERSION: '9.0.x'
  BUILD_CONFIGURATION: 'Release'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore HolyBless.sln

 
      - name: Build
        env:
          ASPNETCORE_ENVIRONMENT: QA
          DOTNET_ENVIRONMENT: QA
        run: dotnet build HolyBless.sln --no-restore --configuration ${{ env.BUILD_CONFIGURATION }}

      - name: Test
        run: dotnet test HolyBless.sln --no-build --configuration ${{ env.BUILD_CONFIGURATION }} --settings coverlet.runsettings

      # - name: Create QA Migration
      #   run: |
      #     cd src/HolyBless.DbMigrator
      #     dotnet run --configuration ${{ env.BUILD_CONFIGURATION }} -- --environment qa

      # - name: Upload build artifacts
      #   uses: actions/upload-artifact@v3
      #   with:
      #     name: backend-qa-build
      #     path: |
      #       src/HolyBless.HttpApi.Host/bin/Release/net9.0/
      #       src/HolyBless.DbMigrator/bin/Release/net9.0/
