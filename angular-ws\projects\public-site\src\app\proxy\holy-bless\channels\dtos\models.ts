import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { ChannelSource } from '../../enums/channel-source.enum';

export interface ChannelDto extends EntityDto<number> {
  parentChannelId?: number;
  contentCode?: string;
  languageCode?: string;
  name?: string;
  weight: number;
  channelSource?: ChannelSource;
}

export interface ChannelSearchDto extends PagedAndSortedResultRequestDto {
  languageCode?: string;
  contentCode?: string;
}

export interface ChannelTreeDto {
  id: number;
  name?: string;
  contentCode?: string;
  isRoot: boolean;
  channelSource?: ChannelSource;
  children: ChannelTreeDto[];
}
