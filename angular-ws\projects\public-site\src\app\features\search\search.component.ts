import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-search',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="search-container min-h-screen bg-gray-50">
      <!-- 导航栏 -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a routerLink="/home" class="text-2xl font-bold text-blue-600">HolyBless</a>
              <span class="ml-4 text-gray-500">/ 搜索</span>
            </div>
            <div class="flex items-center space-x-6">
              <a routerLink="/ebooks" class="text-gray-700 hover:text-blue-600 font-medium">电子书</a>
              <a routerLink="/storage" class="text-gray-700 hover:text-blue-600 font-medium">网盘</a>
              <a routerLink="/podcast" class="text-gray-700 hover:text-blue-600 font-medium">播客</a>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <!-- 搜索标题 -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">搜索您的内容</h1>
          <p class="text-gray-600">在电子书、网盘文件、播客中查找您需要的内容</p>
        </div>

        <!-- 搜索框 -->
        <div class="mb-8">
          <div class="relative">
            <input 
              type="text" 
              [(ngModel)]="searchQuery"
              placeholder="输入关键词搜索..."
              class="w-full px-4 py-4 pl-12 pr-24 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <span class="text-gray-400 text-xl">🔍</span>
            </div>
            <button 
              (click)="performSearch()"
              class="absolute inset-y-0 right-0 pr-4 flex items-center">
              <span class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">搜索</span>
            </button>
          </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="mb-8">
          <div class="flex flex-wrap gap-4 justify-center">
            <button 
              *ngFor="let filter of searchFilters"
              (click)="selectFilter(filter.value)"
              [class]="getFilterClass(filter.value)"
              class="px-4 py-2 rounded-lg font-medium transition-colors">
              {{filter.icon}} {{filter.label}}
            </button>
          </div>
        </div>

        <!-- 搜索建议 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">热门搜索</h2>
          <div class="flex flex-wrap gap-2">
            <span 
              *ngFor="let suggestion of searchSuggestions"
              (click)="searchSuggestion(suggestion)"
              class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 cursor-pointer transition-colors">
              {{suggestion}}
            </span>
          </div>
        </div>

        <!-- 最近搜索 -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">最近搜索</h2>
          <div class="space-y-2">
            <div 
              *ngFor="let recent of recentSearches"
              (click)="searchSuggestion(recent.query)"
              class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
              <div class="flex items-center">
                <span class="text-gray-400 mr-3">🕒</span>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{recent.query}}</p>
                  <p class="text-xs text-gray-600">{{recent.category}} • {{recent.resultsCount}} 个结果</p>
                </div>
              </div>
              <span class="text-xs text-gray-500">{{recent.time}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class SearchComponent {
  searchQuery: string = '';
  selectedFilter: string = 'all';
  
  searchFilters = [
    { value: 'all', label: '全部', icon: '🔍' },
    { value: 'ebooks', label: '电子书', icon: '📚' },
    { value: 'storage', label: '网盘文件', icon: '☁️' },
    { value: 'podcast', label: '播客', icon: '🎧' }
  ];

  searchSuggestions = [
    'JavaScript', '人工智能', '项目管理', 'Python编程', '商业分析', 
    '设计思维', '科技前沿', '健康生活', '投资理财', '历史文献'
  ];

  recentSearches = [
    { query: 'React 教程', category: '电子书', resultsCount: 15, time: '2小时前' },
    { query: '项目文档', category: '网盘文件', resultsCount: 8, time: '1天前' },
    { query: '科技播客', category: '播客', resultsCount: 23, time: '3天前' },
    { query: 'Vue.js', category: '电子书', resultsCount: 12, time: '1周前' }
  ];

  selectFilter(filter: string) {
    this.selectedFilter = filter;
  }

  getFilterClass(filterValue: string): string {
    const baseClass = 'px-4 py-2 rounded-lg font-medium transition-colors';
    if (this.selectedFilter === filterValue) {
      return baseClass + ' bg-blue-600 text-white';
    }
    return baseClass + ' bg-white text-gray-700 border border-gray-300 hover:bg-gray-50';
  }

  performSearch() {
    if (this.searchQuery.trim()) {
      console.log('Searching for:', this.searchQuery, 'in:', this.selectedFilter);
      // 这里可以导航到搜索结果页面
    }
  }

  searchSuggestion(suggestion: string) {
    this.searchQuery = suggestion;
    this.performSearch();
  }
}
