name: CD - Dev Deploy Public Site to Cloudflare Pages

on:
  workflow_run:
    workflows: ['FE - Dev Build Angular Workspace']
    types:
      - completed
    branches: [ main ]

jobs:
  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: angular-ws
        
    permissions:
      contents: read
      deployments: write
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'yarn'
          cache-dependency-path: angular-ws/yarn.lock
      
      - name: Install dependencies
        run: yarn install

      - name: Build public-site
        run: npx ng build public-site -c production
      
      - name: Build admin-site
        run: npx ng build admin-site -c production

      - name: Change to angular-ws directory
        run: |
          cd $GITHUB_WORKSPACE/angular-ws
          echo "Current directory: $(pwd)"
          echo "Build output exists: $(ls -la dist/public-site)"
      
      - name: Move build output to parent directory
        run: |
          mkdir -p ../dist/public-site
          mv dist/public-site/browser/* ../dist/public-site/
          ls -la ../dist/public-site

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: holybless-public
          directory: dist/public-site

      - name: Move build output to parent directory
        run: |
          mkdir -p ../dist/admin-site
          mv dist/admin-site/browser/* ../dist/admin-site/
          ls -la ../dist/admin-site

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: holybless-admin
          directory: dist/admin-site

          # Optional: Add custom domain if you have one
          # customDomain: www.yoursite.com
