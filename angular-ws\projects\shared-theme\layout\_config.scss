.layout-config-sidebar {
    &.p-sidebar {
        .p-sidebar-content {
            padding-left: 2rem;
            padding-right: 2rem;
        }
    }
}

.layout-config-button {
    display: block;
    position: fixed;
    width: 3rem;
    height: 3rem;
    line-height: 3rem;
    background: var(--primary-color);
    color: var(--primary-color-text);
    text-align: center;
    top: 50%;
    right: 0;
    margin-top: -1.5rem;
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    transition: background-color var(--transition-duration);
    overflow: hidden;
    cursor: pointer;
    z-index: 999;
    box-shadow: -0.25rem 0 1rem rgba(0, 0, 0, 0.15);

    &.config-link {
        font-size: 1rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
        border: none;
    }
    .config-link:focus {
        outline: 0 none;
        outline-offset: 0;
        box-shadow: 0 0 0 1px var(--focus-ring--shadow);
    }

    i {
        font-size: 2rem;
        line-height: inherit;
        transform: rotate(0deg);
        transition: transform 1s;
    }

    &:hover {
        background: var(--p-primary-400);
    }
}

.app-config-mobile-button {
    display: none;
}

.app-config-button {
    border: 1px solid var(--border-color);
    background-color: var(--card-background);
    position: relative;
    z-index: 0;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    text-align: center;

    i {
        line-height: inherit;
        font-size: 1.125rem;
    }
    &::before,
    &::after {
        content: '';
        position: absolute;
        inset: 0;
        border: 1px solid var(--topbar-item-text-color);
        transition: all 0.5s;
        animation: clippath 10s infinite linear;
        border-radius: 50%;
        opacity: 1;
    }

    &::after {
        animation: clippath 10s infinite -5s linear;
    }
}

@keyframes clippath {
    0% {
        clip-path: inset(0 0 98% 0);
    }
    12.5% {
        clip-path: inset(0 0 60% 0);
    }
    25% {
        clip-path: inset(0 98% 0 0);
    }
    37.5% {
        clip-path: inset(0 60% 0 0);
    }
    50% {
        clip-path: inset(98% 0 0 0);
    }
    62.5% {
        clip-path: inset(60% 0 0 0);
    }
    75% {
        clip-path: inset(0 0 0 98%);
    }
    87.5% {
        clip-path: inset(0 0 0 60%);
    }
    100% {
        clip-path: inset(0 0 98% 0);
    }
}
