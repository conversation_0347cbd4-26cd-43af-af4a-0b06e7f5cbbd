import { Component, signal, computed, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Table, TableModule } from 'primeng/table';
import { CheckboxModule, CheckboxChangeEvent } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FileUploadModule } from 'primeng/fileupload';
import { FormsModule } from '@angular/forms';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TagModule } from 'primeng/tag';
import { DropdownModule } from 'primeng/dropdown';
import { DialogModule } from 'primeng/dialog';
import { PickListModule } from 'primeng/picklist';
import { filter } from 'rxjs';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';


@Component({
  selector: 'app-channeltable',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    CheckboxModule,
    ButtonModule,
    InputTextModule,
    FileUploadModule,
    FormsModule,
    IconFieldModule,
    InputIconModule,
    TagModule,
    DropdownModule,
    DialogModule,
    PickListModule,
    ConfirmDialogModule,
    ToastModule,
  ],
  providers: [ConfirmationService, MessageService],
  template: `
    <div class="card">
      <!-- 工具栏（全选/Import/Remove按钮） -->
     <div class="flex gap-2 items-center mb-4">
     <button
          pButton
          icon="pi pi-trash"
          class="p-button-danger"
          label="Remove"
          (click)="confirmDelete()"
        ></button>
        <p-confirmDialog
          header="确认删除"
          icon="pi pi-exclamation-triangle"
          acceptButtonStyleClass="p-button-danger"
        ></p-confirmDialog>
        <p-toast></p-toast>
      </div>

      <!-- 表格 -->
      <p-table 
       #dt
        [value]="filteredFiles()"
        [(selection)]="selectedFile"
        (selectionChange)="selectedFile.set($event)"
        dataKey="id"
      >
        <ng-template pTemplate="header">
          <tr>
           <th style="width: 3rem"></th>
            <th pSortableColumn="title">Title <p-sortIcon field="title"></p-sortIcon></th>
          </tr>
        </ng-template>
        
        <ng-template pTemplate="body" let-file>
          <tr>
            <td>
             <p-checkbox
              [ngModel]="isFileSelected(file)"
              (onChange)="toggleFileSelection(file, $event)"
              [binary]="true"
             ></p-checkbox>
            </td>
            <td>
              <span >{{ file.title }}</span>
            
            </td>
          
          </tr>
        </ng-template>
      </p-table>
    </div>
  `,
  styles: [`
    .p-checkbox {
      margin-right: 0.5rem;
    }
  `]
})
export class ChannelTableComponent {
  confirmationService = inject(ConfirmationService);
  messageService = inject(MessageService);
  private _files = signal<any[]>([]);
  @Input() set files(value: any[]) {
    this._files.set(value);
  }
  get files() {
    return this._files();
  }

  @Input() selectedFolderId: string | null = null;
  @Input() selectedFolderLabel: string | null = null;
  displayImportDialog = false;
  availableFiles = [
    { id: 1, name: 'document.pdf' },
    { id: 2, name: 'image.png' },
    { id: 3, name: 'report.docx' }
  ];

  editingRowId: number | null = null;
  contentCategories = ['PDF', 'Doc', 'Excel', 'Image', 'Text']



  // 选中状态信号（关键修复：初始化空数组）
  selectedFile = signal<any | null>(null); // 定义信号

  confirmDelete() {
    if (!this.selectedFile()) {
      this.messageService.add({
        severity: 'warn',
        summary: '未选择文件',
        detail: '请先选择要删除的文件'
      });
      return;
    }

    const fileName = this.selectedFile()!.title;
    const hasFolder = this.selectedFile()!.contentCategory === 'folder';

    this.confirmationService.confirm({
      message: `确定要删除选中的 ${this.selectedFile().title} 文件吗？${hasFolder ? '（包含文件夹）' : ''}`,
      header: '删除确认',
      icon: 'pi pi-info-circle',
      acceptLabel: '删除',
      rejectLabel: '取消',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.deleteSelectedFiles();
        this.messageService.add({
          severity: 'success',
          summary: '删除成功',
          detail: `已删除: ${fileName}`
        });
      }
    });
  }

  deleteSelectedFiles() {
    const selectedFileId = this.selectedFile().id;
    this._files.update(current =>
      current.filter(file => !(selectedFileId === file.id))
    );

    this.selectedFile.set(null);
  }

  showImportDialog() {
    this.displayImportDialog = true;
  }


  // 在组件类中添加
  startEdit(rowId: number) {
    this.editingRowId = rowId;
  }

  cancelEdit() {
    this.editingRowId = null;
  }

  saveEdit() {
    this.editingRowId = null;
    // 这里可以添加保存到API的逻辑
  }

  // 修改判断单行是否选中的方法
  isFileSelected(file: any): boolean {
    const selected = this.selectedFile();
    return selected ? selected.id === file.id : false;
  }

  // 修改单行切换方法
  toggleFileSelection(file: any, event: CheckboxChangeEvent) {
    const isChecked = event.checked;
    this.selectedFile.set(isChecked ? file : null);
  }

  // 以下是原有方法（完全保持不变）
  globalSearchText = '';
  filteredFiles = computed(() => {
    if (!this.globalSearchText) return this._files();
    const searchTerm = this.globalSearchText.toLowerCase();
    return this._files().filter(file =>
      file.title.toLowerCase().includes(searchTerm) ||
      file.contentCategory.toLowerCase().includes(searchTerm) ||
      file.fileTitle.toLowerCase().includes(searchTerm)
    );
  });

  uploadFile(event: any) {
    console.log('File upload triggered', event);
  }




}
