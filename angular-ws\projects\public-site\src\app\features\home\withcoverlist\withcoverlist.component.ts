import { Component, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-withcoverlist',
  standalone: true,
  imports: [CommonModule, CardModule, PaginatorModule, CalendarModule, ButtonModule, FormsModule],
  templateUrl: './withcoverlist.component.html',
  styleUrls: ['./withcoverlist.component.scss'],
})
export class WithcoverlistComponent {
  // 分页相关属性
  totalRecords = 50;
  rows = 9;
  first = 0;
  private _isMobile = false;

  // 日期选择器属性
  selectedDate: Date = new Date();

  // 模拟数据数组
  cardItems = [
    {
      id: 1,
      title: '示例标题 1',
      subtitle: '2024年1月1日',
      content: '这是一个使用 PrimeNG Card 组件的示例内容。卡片包含了标题、副标题、封面图片和描述文本。',
      imageUrl: 'https://via.placeholder.com/400x250/4f46e5/ffffff?text=封面1',
      viewCount: 123
    },
    {
      id: 2,
      title: '示例标题 2',
      subtitle: '2024年1月2日',
      content: '第二个示例卡片，展示不同的内容和样式。PrimeNG Card 组件提供了丰富的模板插槽。',
      imageUrl: 'https://via.placeholder.com/400x250/059669/ffffff?text=封面2',
      viewCount: 456
    },
    {
      id: 3,
      title: '示例标题 3',
      subtitle: '2024年1月3日',
      content: '第三个示例卡片，演示了如何使用 PrimeNG 的 Card 组件创建美观的内容展示效果。',
      imageUrl: 'https://via.placeholder.com/400x250/dc2626/ffffff?text=封面3',
      viewCount: 789
    },
    {
      id: 4,
      title: '示例标题 4',
      subtitle: '2024年1月4日',
      content: '第四个示例卡片，用于演示分页功能的效果。',
      imageUrl: 'https://via.placeholder.com/400x250/7c3aed/ffffff?text=封面4',
      viewCount: 321
    },
    {
      id: 5,
      title: '示例标题 5',
      subtitle: '2024年1月5日',
      content: '第五个示例卡片，展示更多内容项目。',
      imageUrl: 'https://via.placeholder.com/400x250/ea580c/ffffff?text=封面5',
      viewCount: 654
    },
    {
      id: 6,
      title: '示例标题 6',
      subtitle: '2024年1月6日',
      content: '第六个示例卡片，完善网格布局的展示效果。',
      imageUrl: 'https://via.placeholder.com/400x250/10b981/ffffff?text=封面6',
      viewCount: 987
    }
  ];

  // 获取当前页显示的数据
  get displayedItems() {
    const startIndex = this.first;
    const endIndex = this.first + this.rows;
    return this.cardItems.slice(startIndex, endIndex);
  }

  constructor() {
    this.checkMobile();
    this.totalRecords = this.cardItems.length;
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [6, 9, 12, 18];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  // 分页事件处理
  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    console.log('页面变化:', event);
    // 这里可以添加数据加载逻辑
  }

  // 日期选择事件处理
  onDateChange(event: Date) {
    console.log('选择的日期:', event);
    // 这里可以添加按日期筛选数据的逻辑
  }

  // 自动播放功能
  isPlaying: boolean = false;
  playInterval: any;

  // 播放本页
  playCurrentPage() {
    if (this.isPlaying) {
      this.stopPlaying();
      return;
    }

    this.isPlaying = true;
    let currentIndex = 0;
    const items = this.displayedItems;
    
    // 每3秒自动滚动到下一张
    this.playInterval = setInterval(() => {
      // 找到当前卡片元素并滚动到视图
      const cardElement = document.getElementById(`card-${items[currentIndex].id}`);
      if (cardElement) {
        cardElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 添加高亮效果
        cardElement.classList.add('playing');
        setTimeout(() => {
          cardElement.classList.remove('playing');
        }, 2800); // 略小于间隔时间，让效果在下一次播放前消失
      }
      
      currentIndex++;
      if (currentIndex >= items.length) {
        // 播放完本页后停止
        this.stopPlaying();
      }
    }, 3000);
  }

  stopPlaying() {
    this.isPlaying = false;
    if (this.playInterval) {
      clearInterval(this.playInterval);
      this.playInterval = null;
    }
    // 移除所有可能的播放效果
    document.querySelectorAll('.playing').forEach(el => {
      el.classList.remove('playing');
    });
  }
}
