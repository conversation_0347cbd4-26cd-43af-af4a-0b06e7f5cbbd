# Project identification
sonar.projectKey=holybless-frontend
sonar.organization=${SONAR_ORGANIZATION}

# Source code location
sonar.sources=projects/public-site/src,projects/admin-site/src,projects/shared-lib/src
sonar.tests=projects/public-site/src,projects/admin-site/src,projects/shared-lib/src
sonar.test.inclusions=**/*.spec.ts
sonar.typescript.lcov.reportPaths=projects/*/coverage/lcov.info
sonar.javascript.lcov.reportPaths=projects/*/coverage/lcov.info

# Exclude patterns
sonar.exclusions=**/node_modules/**,**/*.spec.ts,**/dist/**,**/coverage/**,**/environments/**,**/test.ts
sonar.coverage.exclusions=**/*.spec.ts,**/test/**,**/tests/**,**/mock/**,**/mocks/**,**/environments/**

# TypeScript specific
sonar.typescript.tsconfigPath=tsconfig.json

# Project metadata
sonar.projectName=HolyBless Frontend
sonar.projectVersion=1.0
sonar.sourceEncoding=UTF-8
