import { Routes } from '@angular/router';

export const PODCAST_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./podcast.component').then(m => m.PodcastComponent),
    title: 'HolyBless - 播客'
  },
  // {
  //   path: 'player/:id',
  //   loadComponent: () => import('./player/player.component').then(m => m.PlayerComponent),
  //   title: 'HolyBless - 播客播放器'
  // },
  // {
  //   path: 'subscriptions',
  //   loadComponent: () => import('./subscriptions/subscriptions.component').then(m => m.SubscriptionsComponent),
  //   title: 'HolyBless - 我的订阅'
  // }
];
