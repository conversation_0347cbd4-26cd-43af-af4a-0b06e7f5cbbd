import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-podcast',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="podcast-container min-h-screen bg-gray-50">
      <!-- 导航栏 -->
      <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a routerLink="/home" class="text-2xl font-bold text-blue-600">HolyBless</a>
              <span class="ml-4 text-gray-500">/ 播客</span>
            </div>
            <div class="flex items-center space-x-6">
              <a routerLink="/podcast/subscriptions" class="text-gray-700 hover:text-blue-600 font-medium">我的订阅</a>
              <a routerLink="/search" class="text-gray-700 hover:text-blue-600 font-medium">搜索</a>
            </div>
          </div>
        </div>
      </nav>

      <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">播客中心</h1>
          <p class="text-gray-600">发现精彩的播客内容，随时随地收听</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 侧边栏 -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">分类</h3>
              <div class="space-y-2">
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">🎯 推荐</span>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">📰 新闻</span>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">💼 商业</span>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">🔬 科技</span>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">🎭 娱乐</span>
                </button>
                <button class="w-full text-left p-2 hover:bg-gray-50 rounded-lg transition-colors">
                  <span class="text-sm text-gray-700">📚 教育</span>
                </button>
              </div>
            </div>

            <!-- 正在播放 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">正在播放</h3>
              <div class="text-center">
                <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg aspect-square mb-3 flex items-center justify-center">
                  <span class="text-white text-2xl">🎧</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900 mb-1">科技前沿</h4>
                <p class="text-xs text-gray-600 mb-3">第 42 期：AI 的未来</p>
                <div class="flex items-center justify-center space-x-3">
                  <button class="text-gray-400 hover:text-gray-600">⏮️</button>
                  <button class="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-purple-700">
                    ⏸️
                  </button>
                  <button class="text-gray-400 hover:text-gray-600">⏭️</button>
                </div>
                <div class="mt-3">
                  <div class="w-full bg-gray-200 rounded-full h-1">
                    <div class="bg-purple-600 h-1 rounded-full" style="width: 35%"></div>
                  </div>
                  <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span>12:30</span>
                    <span>35:45</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 主要内容区域 -->
          <div class="lg:col-span-3 space-y-6">
            <!-- 推荐播客 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">推荐播客</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="podcast-item cursor-pointer" (click)="playPodcast('1')">
                  <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="bg-blue-500 rounded-lg w-16 h-16 mr-4 flex items-center justify-center">
                      <span class="text-white text-xl">🎙️</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">科技前沿</h3>
                      <p class="text-xs text-gray-600 mb-1">探索最新科技趋势</p>
                      <p class="text-xs text-gray-500">42 集 • 每周更新</p>
                    </div>
                  </div>
                </div>

                <div class="podcast-item cursor-pointer" (click)="playPodcast('2')">
                  <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="bg-green-500 rounded-lg w-16 h-16 mr-4 flex items-center justify-center">
                      <span class="text-white text-xl">🎙️</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">商业洞察</h3>
                      <p class="text-xs text-gray-600 mb-1">商业世界的深度分析</p>
                      <p class="text-xs text-gray-500">28 集 • 每周更新</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 最新节目 -->
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">最新节目</h2>
              <div class="space-y-4">
                <div class="episode-item cursor-pointer" (click)="playEpisode('ep1')">
                  <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="bg-purple-500 rounded w-12 h-12 mr-4 flex items-center justify-center">
                      <span class="text-white">🎵</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">AI 革命：机器学习的新突破</h3>
                      <p class="text-xs text-gray-600">科技前沿 • 35 分钟</p>
                      <p class="text-xs text-gray-500">2024年6月30日</p>
                    </div>
                    <button class="text-purple-600 hover:text-purple-800 ml-4">
                      ▶️
                    </button>
                  </div>
                </div>

                <div class="episode-item cursor-pointer" (click)="playEpisode('ep2')">
                  <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="bg-blue-500 rounded w-12 h-12 mr-4 flex items-center justify-center">
                      <span class="text-white">🎵</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">创业故事：从0到1的成功之路</h3>
                      <p class="text-xs text-gray-600">商业洞察 • 42 分钟</p>
                      <p class="text-xs text-gray-500">2024年6月29日</p>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 ml-4">
                      ▶️
                    </button>
                  </div>
                </div>

                <div class="episode-item cursor-pointer" (click)="playEpisode('ep3')">
                  <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="bg-green-500 rounded w-12 h-12 mr-4 flex items-center justify-center">
                      <span class="text-white">🎵</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-sm font-medium text-gray-900">健康生活：现代人的养生之道</h3>
                      <p class="text-xs text-gray-600">生活方式 • 28 分钟</p>
                      <p class="text-xs text-gray-500">2024年6月28日</p>
                    </div>
                    <button class="text-green-600 hover:text-green-800 ml-4">
                      ▶️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class PodcastComponent {
  playPodcast(podcastId: string) {
    console.log('Playing podcast:', podcastId);
  }

  playEpisode(episodeId: string) {
    console.log('Playing episode:', episodeId);
  }
}
