import type { VirtualFolderTreeDto } from './models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyVirtualFolderService {
  apiName = 'Default';
  

  getFolderTreeJsonByRootFolderId = (rootFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'GET',
      responseType: 'text',
      url: `/api/app/read-only-virtual-folder/folder-tree-json/${rootFolderId}`,
    },
    { apiName: this.apiName,...config });
  

  getFolderTreeWithFiles = (rootFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, VirtualFolderTreeDto>({
      method: 'GET',
      url: `/api/app/read-only-virtual-folder/folder-tree-with-files/${rootFolderId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
