import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="not-found-container min-h-screen bg-gray-50 flex items-center justify-center">
      <div class="text-center">
        <div class="mb-8">
          <h1 class="text-9xl font-bold text-gray-300">404</h1>
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-4">页面未找到</h2>
        <p class="text-gray-600 mb-8 max-w-md">
          抱歉，您访问的页面不存在或已被移动。请检查网址是否正确，或返回首页继续浏览。
        </p>
        <div class="space-x-4">
          <a routerLink="/landing" 
             class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
            返回首页
          </a>
          <a routerLink="/home" 
             class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
            主站首页
          </a>
        </div>
        
        <!-- 建议链接 -->
        <div class="mt-12">
          <h3 class="text-lg font-medium text-gray-900 mb-4">您可能感兴趣的页面：</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <a routerLink="/ebooks" class="text-blue-600 hover:text-blue-800 text-sm">📚 电子书</a>
            <a routerLink="/storage" class="text-blue-600 hover:text-blue-800 text-sm">☁️ 网盘</a>
            <a routerLink="/podcast" class="text-blue-600 hover:text-blue-800 text-sm">🎧 播客</a>
            <a routerLink="/help" class="text-blue-600 hover:text-blue-800 text-sm">❓ 帮助</a>
          </div>
        </div>
      </div>
    </div>
  `
})
export class NotFoundComponent {
}
