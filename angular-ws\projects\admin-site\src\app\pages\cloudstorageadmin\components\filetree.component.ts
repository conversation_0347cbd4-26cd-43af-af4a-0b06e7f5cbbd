import {
  Component,
  signal,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CommonModule } from '@angular/common';
import { DragDropModule, CdkDragDrop } from '@angular/cdk/drag-drop';
import { FormsModule } from '@angular/forms'; // 新增导入
import { PickListModule } from 'primeng/picklist';
import { CheckboxModule, CheckboxChangeEvent } from 'primeng/checkbox'; // 新增导入
import { ConfirmDialog, ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
@Component({
  selector: 'app-file-tree',
  standalone: true,
  imports: [
    DragDropModule,
    TreeModule,
    ButtonModule,
    DialogModule,
    CommonModule,
    FormsModule,
    PickListModule,
    CheckboxModule,
    ConfirmDialogModule,
    ToastModule,
  ],
  template: `
    <div class="card">
      <div class="flex gap-2 items-center mb-4">
        <p-button
          icon="pi pi-plus"
          label=""
          [disabled]="!selectedNode"
          (click)="addNode()"
        >
        </p-button>

        <p-button
          icon="pi pi-trash"
          label=""
          severity="danger"
          [disabled]="!selectedNode"
          (click)="confirmDelete()"
        >
        </p-button>

        <p-button
          icon="pi pi-pencil"
          label=""
          [disabled]="!selectedNode"
          (click)="startEdit()"
        ></p-button>
        <button
          *ngIf="editingText"
          pButton
          icon="pi pi-check"
          class="p-button-rounded p-button-text p-button-success mr-2"
          (click)="saveEdit()"
        ></button>
        <button
          *ngIf="editingText"
          pButton
          icon="pi pi-times"
          class="p-button-rounded p-button-text p-button-danger"
          (click)="cancelEdit()"
        ></button>
        <p-button
          [icon]="isFolded() ? 'pi pi-chevron-right' : 'pi pi-chevron-down'"
          [label]=""
          severity="help"
          (click)="toggleFoldAll()"
          [disabled]="!selectedNode"
        ></p-button>
        <button
          *ngIf="showAddToChannelButton()"
          pButton
          icon="pi pi-upload"
          label="AddToChannel"
          [disabled]="!selectedFolderId"
          (click)="showImportDialog()"
          [disabled]="!selectedNode"
        ></button>
      </div>
      <!-- 导入对话框 -->
      <p-dialog
        header="Add To Channel"
        [(visible)]="displayImportDialog"
        [modal]="true"
        [style]="{ width: '50vw' }"
      >
        <p-tree
          [value]="folders()"
          selectionMode="single"
          [(selection)]="selectedNode"
          (onNodeSelect)="onNodeSelect($event)"
        >
          <ng-template let-node pTemplate="default">
            <div class="node-container">
              <p-checkbox
                [ngModel]="selectedNode === node"
                (onChange)="toggleNodeSelection(node, $event)"
                [binary]="true"
              ></p-checkbox>
              <span>{{ node.label }}</span>
            </div>
          </ng-template>
        </p-tree>
        <ng-template pTemplate="footer">
          <button
            pButton
            label="取消"
            icon="pi pi-times"
            class="p-button-text"
            (click)="displayImportDialog = false"
          ></button>
          <button
            pButton
            label="确认保存"
            icon="pi pi-check"
            class="p-button-success"
            (click)="confirmSave()"
          ></button>
        </ng-template>
      </p-dialog>
      <p-tree
        #treeRef
        [value]="folders()"
        selectionMode="single"
        [(selection)]="selectedNode"
        (onNodeSelect)="onNodeSelect($event)"
        cdkDropList
        (cdkDropListDropped)="onDrop($event)"
      >
        <ng-template let-node pTemplate="default">
          <div
            cdkDrag
            [cdkDragData]="node"
            (mouseenter)="setHoveredNode($event)"
            class="node-container"
          >
            <p-checkbox
              [ngModel]="selectedNode === node"
              (onChange)="toggleNodeSelection(node, $event)"
              [binary]="true"
            ></p-checkbox>
            <span *ngIf="!editingText">{{ node.label }}</span>
            <input *ngIf="editingText" [(ngModel)]="node.label" pInputText />

            <!-- 添加放置区域提示 -->
            <div
              *ngIf="!editingText"
              class="drop-zone"
              (mouseenter)="setDropTarget(node)"
            ></div>
          </div>
        </ng-template>
      </p-tree>

      <p-dialog
        header="Confirm"
        [(visible)]="showDeleteDialog"
        [style]="{ width: '350px' }"
      >
        <div class="confirmation-content">
          <i
            class="pi pi-exclamation-triangle mr-3"
            style="font-size: 2rem"
          ></i>
          <span>This node has children. Are you sure to delete?</span>
        </div>
        <ng-template pTemplate="footer">
          <p-button
            icon="pi pi-times"
            label="No"
            (click)="showDeleteDialog = false"
          >
          </p-button>
          <p-button
            icon="pi pi-check"
            label="Yes"
            (click)="deleteNode()"
            severity="danger"
          >
          </p-button>
        </ng-template>
      </p-dialog>
    </div>
  `,
  styles: [
    `
      .node-container {
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .drop-zone {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: rgba(0, 255, 0, 0.1);
        z-index: 10;
        opacity: 0;
        transition: opacity 0.2s;
      }

      .drop-zone:hover {
        opacity: 1;
      }
      .cdk-drag-preview {
        opacity: 0.8;
        background: var(--surface-a);
        border: 2px dashed var(--primary-color); /* 修改点2：虚线边框提示嵌套 */
        transform: scale(0.95);
      }
    `,
  ],
})
export class FileTreeComponent {
  @Input() showAddToChannelButton = signal(true); // 被引用时隐藏按需隐藏按钮
  @ViewChild('treeRef', { static: true })
  treeRef!: ElementRef<HTMLElement>; // 新增这行
  @Input() folders = signal<TreeNode[]>([]);
  @Input() selectedFolderId: string | null = null;
  @Input() selectedFolderLabel: string | null = null;

  @Output() foldersChange = new EventEmitter<TreeNode[]>();
  @Output() nodeSelected = new EventEmitter<TreeNode | null>();

  displayImportDialog = false;
  availableFiles = [
    { id: 1, name: 'document.pdf' },
    { id: 2, name: 'image.png' },
    { id: 3, name: 'report.docx' },
  ];
  // 选中状态信号（关键修复：初始化空数组）
  selectedFiles = signal<any[]>([]);

  hoveredNode = signal<TreeNode | null>(null);
  selectedNode: TreeNode | null = null;
  showDeleteDialog = false;

  // 新增状态变量
  showRenameDialog = false;
  // 新增状态控制
  isFolded = signal(true);

  editingText: string | undefined = undefined;

  confirmSave() {
    console.log('保存文件夹结构:', this.folders());
    this.displayImportDialog = false;
    // 这里添加实际保存逻辑
  }

  // 在组件类中添加
  startEdit() {
    this.editingText = this.selectedNode?.label;
  }

  cancelEdit() {
    this.selectedNode!.label = this.editingText;
    this.editingText = undefined;
  }

  saveEdit() {
    this.editingText = undefined;
    // 这里可以添加保存到API的逻辑
  }

  toggleNodeSelection(node: TreeNode, event: CheckboxChangeEvent) {
    const isChecked = event.checked;
    this.selectedNode = isChecked ? node : null;
  }
  // 节点选择事件
  onNodeSelect(event: { node: TreeNode }) {
    this.selectedNode = event.node;
    this.nodeSelected.emit(event.node);
  }
  showImportDialog() {
    this.displayImportDialog = true;
  }
  importFiles() {
    console.log(
      `Importing to folder ${this.selectedFolderId}:`,
      this.selectedFiles,
    );
    this.displayImportDialog = false;
    // 这里可以添加实际导入逻辑
  }
  // 切换全部折叠/展开
  toggleFoldAll() {
    this.isFolded.update((val) => !val);
    this.updateAllNodesExpansion(!this.isFolded());
  }
  // 递归更新节点展开状态
  private updateAllNodesExpansion(expanded: boolean) {
    this.folders.update((current) => {
      const updateNodes = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.map((node) => ({
          ...node,
          expanded,
          children: node.children ? updateNodes(node.children) : undefined,
        }));
      };
      return updateNodes(current);
    });
  }

  // 新增方法（与模板中的 (mouseenter)="setDropTarget(node)" 对应）
  setDropTarget(node: TreeNode) {
    this.hoveredNode.set(node); // 直接复用已有的 hoveredNode signal
  }

  getNodeIcon(node: TreeNode): string {
    switch (node.type) {
      case 'folder':
        return 'pi pi-folder';
      case 'file':
        return 'pi pi-file';
      case 'text':
        return 'pi pi-file-text';
      case 'doc':
        return 'pi pi-file-word';
      case 'image':
        return 'pi pi-file-image';
      case 'video':
        return 'pi pi-file-video';
      case 'audio':
        return 'pi pi-file-audio';
      default:
        return 'pi pi-file';
    }
  }
  // 添加新节点
  addNode() {
    if (!this.selectedNode) return;

    const newNode: TreeNode = {
      label: 'New Node',
    };

    if (!this.selectedNode!.children) {
      this.selectedNode!.children = [];
    }

    this.selectedNode!.children!.push(newNode);
    this.folders.update((v) => [...v]);
    this.foldersChange.emit(this.folders());
  }

  // 确认删除
  confirmDelete() {
    if (!this.selectedNode) return;

    if (
      this.selectedNode!.children &&
      this.selectedNode!.children!.length > 0
    ) {
      this.showDeleteDialog = true;
    } else {
      this.deleteNode();
    }
  }
  // 执行删除
  deleteNode() {
    if (!this.selectedNode) return;

    const deleteFromParent = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.filter((node) => {
        if (node.children) {
          node.children = deleteFromParent(node.children);
        }
        return node !== this.selectedNode;
      });
    };

    this.folders.update((v) => deleteFromParent(v));
    this.foldersChange.emit(this.folders());
    this.selectedNode;
    this.showDeleteDialog = false;
  }

  setHoveredNode(event: MouseEvent) {
    if (!this.treeRef?.nativeElement) return; // 新增防御

    const treeEl = this.treeRef.nativeElement;
    const mouseY = event.clientY;

    // 类型安全的节点查找
    const nodeElements = treeEl.querySelectorAll<HTMLElement>('.p-treenode');
    let closestNode: TreeNode | null = null;
    let minDistance = Infinity;

    nodeElements.forEach((el: HTMLElement) => {
      const rect = el.getBoundingClientRect();
      const distance = Math.abs(mouseY - rect.top);
      const context = (el as any).__ngContext__;

      if (distance < minDistance && mouseY <= rect.bottom && context?.node) {
        minDistance = distance;
        closestNode = context.node as TreeNode;
      }
    });

    this.hoveredNode.set(closestNode);
  }
  private handleNodeUpdate() {
    // 通用更新方法
    this.folders.update((v) => [...v]);
    this.foldersChange.emit(this.folders());
  }

  // 强制打印拖拽事件（调试用）
  onDrop(event: CdkDragDrop<TreeNode[]>) {
    console.log('DragDrop Event:', event); // 关键点4：必须看到这个日志
    const draggedNode = event.item.data;
    const targetNode = this.hoveredNode();

    if (
      !targetNode ||
      draggedNode === targetNode ||
      this.isDirectChild(targetNode, draggedNode)
    )
      return;
    // 新增：检查目标节点是否已展开（关键修复）
    if (targetNode.children && !targetNode.expanded) {
      targetNode.expanded = true; // 自动展开目标节点
      setTimeout(() => this.handleDrop(draggedNode, targetNode), 50);
      // 延迟确保DOM更新
    } else {
      this.handleDrop(draggedNode, targetNode);
    }
  }
  private handleDrop(draggedNode: TreeNode, targetNode: TreeNode) {
    // 执行拖拽操作
    const newTree = this.removeNodeFromTree([...this.folders()], draggedNode);
    if (!targetNode.children) targetNode.children = [];
    targetNode.children.push({ ...draggedNode });

    this.folders.set(newTree);
    this.foldersChange.emit(newTree);
  }
  private removeNodeFromTree(nodes: TreeNode[], target: TreeNode): TreeNode[] {
    return nodes.filter((node) => {
      if (node.children) {
        node.children = this.removeNodeFromTree(node.children, target);
      }
      return node !== target;
    });
  }
  private isDirectChild(parent: TreeNode, child: TreeNode): boolean {
    // 仅检查是否是直接子节点
    return !!parent.children?.some((c) => c === child);
  }
  // 新增辅助方法（如果不存在）：
  private isDescendant(dragged: TreeNode, target: TreeNode): boolean {
    if (!target.children) return false;
    return target.children.some(
      (child) => child === dragged || this.isDescendant(dragged, child),
    );
  }
}
