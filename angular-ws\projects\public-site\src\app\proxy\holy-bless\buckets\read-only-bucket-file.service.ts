import type { BucketFileDto, BucketToFileDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyBucketFileService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BucketFileDto>({
      method: 'GET',
      url: `/api/app/read-only-bucket-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<BucketFileDto>>({
      method: 'GET',
      url: '/api/app/read-only-bucket-file',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  listLinkedBucketByFileIdByFileId = (fileId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BucketToFileDto[]>({
      method: 'POST',
      url: `/api/app/read-only-bucket-file/list-linked-bucket-by-file-id/${fileId}`,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
