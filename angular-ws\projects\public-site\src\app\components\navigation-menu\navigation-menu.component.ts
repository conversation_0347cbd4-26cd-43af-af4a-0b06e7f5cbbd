import { Component, OnInit, signal, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MenubarModule } from 'primeng/menubar';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { TooltipModule } from 'primeng/tooltip';
import { MenuModule } from 'primeng/menu';
import { DrawerService } from '../../services/drawer.service';

@Component({
  selector: 'app-navigation-menu',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MenubarModule,
    ButtonModule,
    DropdownModule,
    TooltipModule,
    MenuModule,
  ],
  templateUrl: './navigation-menu.component.html',
  styleUrls: ['./navigation-menu.component.scss'],
})
export class NavigationMenuComponent implements OnInit {
  // 使用signal管理状态
  private _selectedAudioLanguage = signal({ label: '中文', value: 'zh-CN' });
  private _selectedDisplayLanguage = signal({
    label: '简体中文',
    value: 'zh-CN',
  });
  private _isPlaying = signal(false);

  // 只读的computed signals供模板使用
  selectedAudioLanguage = this._selectedAudioLanguage.asReadonly();
  selectedDisplayLanguage = this._selectedDisplayLanguage.asReadonly();
  isPlaying = this._isPlaying.asReadonly();

  // 菜单项
  menuItems: MenuItem[] = [];

  // 音频播放语言选项 - 使用computed来动态生成菜单项
  audioLanguages = computed((): MenuItem[] => [
    {
      label: '中文',
      command: () =>
        this.selectAudioLanguage({ label: '中文', value: 'zh-CN' }),
    },
    {
      label: 'English',
      command: () =>
        this.selectAudioLanguage({ label: 'English', value: 'en-US' }),
    },
    {
      label: '日本語',
      command: () =>
        this.selectAudioLanguage({ label: '日本語', value: 'ja-JP' }),
    },
    {
      label: '한국어',
      command: () =>
        this.selectAudioLanguage({ label: '한국어', value: 'ko-KR' }),
    },
  ]);

  // 页面展示语言选项 - 使用computed来动态生成菜单项
  displayLanguages = computed((): MenuItem[] => [
    {
      label: '简体中文',
      command: () =>
        this.selectDisplayLanguage({ label: '简体中文', value: 'zh-CN' }),
    },
    {
      label: 'English',
      command: () =>
        this.selectDisplayLanguage({ label: 'English', value: 'en-US' }),
    },
    {
      label: '繁體中文',
      command: () =>
        this.selectDisplayLanguage({ label: '繁體中文', value: 'zh-TW' }),
    },
    {
      label: '日本語',
      command: () =>
        this.selectDisplayLanguage({ label: '日本語', value: 'ja-JP' }),
    },
  ]);

  // 播放状态的computed属性
  playButtonIcon = computed(() =>
    this._isPlaying() ? 'pi pi-pause' : 'pi pi-play',
  );
  playButtonTooltip = computed(() => (this._isPlaying() ? '暂停' : '播放'));

  // 注入服务
  private drawerService = inject(DrawerService);

  constructor(private router: Router) {}

  ngOnInit() {
    this.initializeMenuItems();
  }

  private initializeMenuItems() {
    this.menuItems = [
      {
        label: '主站',
        icon: 'pi pi-home',
        command: () => this.router.navigate(['/home']),
      },
      {
        label: '电子书',
        icon: 'pi pi-book',
      },
      {
        label: '网盘',
        icon: 'pi pi-cloud',
      },
      {
        label: '播客',
        icon: 'pi pi-volume-up',
      },
      {
        label: '帮助',
        icon: 'pi pi-question-circle',
      },
    ];
  }

  navigateToHome() {
    this.router.navigate(['/landing']);
  }

  selectAudioLanguage(language: { label: string; value: string }) {
    this._selectedAudioLanguage.set(language);
    console.log('音频语言已更改为:', this._selectedAudioLanguage().label);
    // 这里可以添加实际的音频语言切换逻辑
  }

  selectDisplayLanguage(language: { label: string; value: string }) {
    this._selectedDisplayLanguage.set(language);
    console.log('显示语言已更改为:', this._selectedDisplayLanguage().label);
    // 这里可以添加实际的界面语言切换逻辑
  }

  onAudioLanguageChange(event: any) {
    this._selectedAudioLanguage.set(event.item);
    console.log('音频语言已更改为:', this._selectedAudioLanguage().label);
    // 这里可以添加实际的音频语言切换逻辑
  }

  onDisplayLanguageChange(event: any) {
    this._selectedDisplayLanguage.set(event.value);
    console.log('显示语言已更改为:', this._selectedDisplayLanguage().label);
    // 这里可以添加实际的界面语言切换逻辑
  }

  openSettings() {
    this.drawerService.openSettings();
    console.log('打开设置抽屉');
  }

  togglePlay() {
    this._isPlaying.update((current) => !current);
    this.drawerService.openPlaylist();
    console.log('播放状态:', this._isPlaying() ? '播放中' : '已暂停');
    console.log('打开播放列表抽屉');
    // 这里可以添加实际的音频播放/暂停逻辑
  }
}
