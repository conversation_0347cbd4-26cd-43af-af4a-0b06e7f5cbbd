using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace HolyBless.FakeData
{
    public interface IFakeDataAppService : IApplicationService
    {
        /// <summary>
        /// Generate and save fake data for all three languages (Simplified Chinese, Traditional Chinese, English)
        /// </summary>
        Task GenerateAllLanguagesDataAsync();

        /// <summary>
        /// Generate and save fake data for Simplified Chinese
        /// </summary>
        Task GenerateSimplifiedChineseDataAsync();

        /// <summary>
        /// Generate and save fake data for Traditional Chinese
        /// </summary>
        Task GenerateTraditionalChineseDataAsync();

        /// <summary>
        /// Generate and save fake data for English
        /// </summary>
        Task GenerateEnglishDataAsync();
    }
}