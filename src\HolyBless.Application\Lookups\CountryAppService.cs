using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using HolyBless.Permissions;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using System.Linq.Dynamic.Core;
using HolyBless.Lookups.Dtos;
using Microsoft.Extensions.Caching.Memory;

namespace HolyBless.Lookups
{
    [Authorize(HolyBlessPermissions.Countries.Default)]
    public class CountryAppService : ReadOnlyCountryAppService, ICountryAppService
    {
        public CountryAppService(IRepository<Country, int> repository
            , IMemoryCache memoryCache)
            : base(repository, memoryCache)
        {
        }

        [Authorize(HolyBlessPermissions.Countries.Create)]
        public async Task<CountryDto> CreateAsync(CreateUpdateCountryDto input)
        {
            var country = ObjectMapper.Map<CreateUpdateCountryDto, Country>(input);
            country = await _repository.InsertAsync(country, autoSave: true);
            // Clear cache after deleting a bucket
            _memoryCache.Remove(KeyCountry);
            return ObjectMapper.Map<Country, CountryDto>(country);
        }

        [Authorize(HolyBlessPermissions.Countries.Edit)]
        public async Task<CountryDto> UpdateAsync(int id, CreateUpdateCountryDto input)
        {
            var country = await _repository.GetAsync(id);
            ObjectMapper.Map(input, country);
            await _repository.UpdateAsync(country, autoSave: true);
            // Clear cache after deleting a bucket
            _memoryCache.Remove(KeyCountry);
            return ObjectMapper.Map<Country, CountryDto>(country);
        }

        [Authorize(HolyBlessPermissions.Countries.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
            // Clear cache after deleting a bucket
            _memoryCache.Remove(KeyCountry);
        }
    }
}